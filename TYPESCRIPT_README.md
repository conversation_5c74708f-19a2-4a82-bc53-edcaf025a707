# TypeScript Recursive Text Splitter

A TypeScript port of the recursive text splitter from the cocoindex project. This library provides intelligent text chunking with support for multiple programming languages through Tree-sitter parsing.

## Features

- **Recursive Text Splitting**: Intelligently splits text into chunks of specified sizes with configurable overlap
- **Multi-language Support**: Syntax-aware splitting for 26+ programming languages using Tree-sitter
- **Static Language Mapping**: Fast language lookup using a static mapping table (equivalent to Rust's `TREE_SITTER_LANGUAGE_BY_LANG`)
- **Unicode Support**: Proper handling of Unicode characters and emoji
- **Configurable**: Customizable chunk size, overlap, and language-specific parsing
- **Type-safe**: Full TypeScript support with comprehensive type definitions
- **Lazy Loading**: Languages are loaded on-demand for optimal performance

## Installation

```bash
npm install cocoindex-split-recursively-ts
```

For Tree-sitter language support, install the optional language packages:

```bash
npm install tree-sitter tree-sitter-javascript tree-sitter-python tree-sitter-rust
# ... other language packages as needed
```

## Quick Start

```typescript
import { Factory } from './src/split-recursively';
import { RecursiveChunker } from './src/recursive-chunker';

// Basic text splitting
const text = "This is a long document that needs to be split into smaller chunks for processing.";
const chunker = new RecursiveChunker(text, null, 50, 10);

const result = chunker.splitRootChunk({
  type: 'regexpSepChunk',
  nextRegexpSepId: 0
});

if (result.success) {
  const chunks = result.data;
  chunks.forEach((chunk, index) => {
    console.log(`Chunk ${index}: "${chunk[1]}"`);
  });
}
```

## Advanced Usage

### Language-specific Splitting with Tree-sitter

```typescript
import { getLanguageConfigSync, createParser } from './src/tree-sitter-config';

const code = `
function hello() {
  console.log("Hello, world!");
}

function goodbye() {
  console.log("Goodbye!");
}
`;

// Get language configuration for JavaScript (using static mapping table)
const langConfig = getLanguageConfigSync('javascript');
if (langConfig) {
  // Create Tree-sitter parser
  const parser = createParser(langConfig);
  if (parser) {
    // Parse the code
    const tree = parser.parse(code);

    // Create chunker with language support
    const chunker = new RecursiveChunker(code, langConfig, 100, 20);

    // Split using syntax tree
    const result = chunker.splitRootChunk({
      type: 'treeSitterNode',
      node: tree.rootNode
    });

    if (result.success) {
      console.log(`Split into ${result.data.length} semantic chunks`);
      result.data.forEach((chunk, i) => {
        console.log(`Chunk ${i + 1}: "${chunk[1]}"`);
      });
    }
  }
}
```

### Using the Factory Pattern

```typescript
import { Factory } from './src/split-recursively';

const factory = new Factory();

// Create operation arguments
const args = {
  text: { name: 'text', typ: { /* ... */ }, idx: 0 },
  chunkSize: { name: 'chunk_size', typ: { /* ... */ }, idx: 1 },
  chunkOverlap: null,
  language: null
};

// Build executor
const executor = await factory.buildExecutor({}, args, context);

if (executor.success) {
  const result = await executor.data.evaluate([
    { type: 'basic', value: { type: 'str', value: text } },
    { type: 'basic', value: { type: 'int64', value: 100 } }
  ]);
}
```

## Static Language Mapping

The library uses a static language mapping table (equivalent to Rust's `TREE_SITTER_LANGUAGE_BY_LANG`) for fast language lookup:

```typescript
import {
  getLanguageConfigSync,
  getSupportedLanguages,
  getSupportedExtensions,
  detectLanguageFromExtension,
  isLanguageSupported
} from './src/tree-sitter-config';

// Direct language lookup (like Rust's TREE_SITTER_LANGUAGE_BY_LANG.get())
const jsConfig = getLanguageConfigSync('JavaScript');
const pyConfig = getLanguageConfigSync('.py'); // Works with extensions too

// Get all supported languages
const languages = getSupportedLanguages();
console.log(languages); // ['C', 'C#', 'C++', 'CSS', ...]

// Get all supported file extensions
const extensions = getSupportedExtensions();
console.log(extensions); // ['.c', '.cs', '.cpp', '.css', ...]

// Detect language from filename
const lang = detectLanguageFromExtension('app.rs'); // 'Rust'

// Check if language is supported
const supported = isLanguageSupported('Python'); // true
```

## Supported Languages

The library supports syntax-aware splitting for 26+ languages:

- **C/C++** (`.c`, `.cpp`, `.cc`, `.cxx`, `.h`, `.hpp`)
- **C#** (`.cs`)
- **CSS** (`.css`, `.scss`)
- **Fortran** (`.f`, `.f90`, `.f95`, `.f03`)
- **Go** (`.go`)
- **HTML** (`.html`, `.htm`)
- **Java** (`.java`)
- **JavaScript** (`.js`)
- **JSON** (`.json`)
- **Markdown** (`.md`, `.mdx`)
- **Pascal** (`.pas`, `.dpr`)
- **PHP** (`.php`)
- **Python** (`.py`)
- **R** (`.r`)
- **Ruby** (`.rb`)
- **Rust** (`.rs`)
- **Scala** (`.scala`)
- **SQL** (`.sql`)
- **Swift** (`.swift`)
- **TOML** (`.toml`)
- **TypeScript/TSX** (`.ts`, `.tsx`)
- **XML/DTD** (`.xml`, `.dtd`)
- **YAML** (`.yaml`, `.yml`)

## API Reference

### RecursiveChunker

The main class for text splitting.

```typescript
class RecursiveChunker {
  constructor(
    fullText: string,
    langConfig: LanguageConfig | null,
    chunkSize: number,
    chunkOverlap: number
  )
  
  splitRootChunk(kind: ChunkKind): Result<Array<[RangeValue, string]>>
}
```

### ChunkKind

Defines how text should be split:

```typescript
type ChunkKind = 
  | { type: 'treeSitterNode'; node: any }
  | { type: 'regexpSepChunk'; nextRegexpSepId: number }
  | { type: 'leafText' };
```

### RangeValue

Represents a text range:

```typescript
class RangeValue {
  constructor(start: number, end: number)
  get length(): number
  extractStr(text: string): string
  toTuple(): [number, number]
  static fromTuple([start, end]: [number, number]): RangeValue
}
```

## Configuration

### Text Separators

The library uses a hierarchy of text separators:

1. Double newlines and more (`\n\n+`)
2. Single newlines (`\n`)
3. Whitespace (`\s+`)

### Language Configuration

Each supported language has:

- **Name**: Human-readable language name
- **Aliases**: File extensions and alternative names
- **Tree-sitter Language**: Parser for syntax-aware splitting
- **Terminal Node Kinds**: Node types that should not be split further

## Testing

Run the basic test suite:

```bash
npm test
```

Run Tree-sitter integration tests:

```bash
npm run test:tree-sitter
```

Run static mapping table tests:

```bash
npm run test:static-mapping
```

Run tests in development mode:

```bash
npm run dev
npm run test:tree-sitter:dev
npm run test:static-mapping:dev
```

Run examples:

```bash
npm run example
```

## Building

Compile TypeScript to JavaScript:

```bash
npm run build
```

## Performance Considerations

- **Chunk Size**: Larger chunks reduce processing overhead but may impact downstream processing
- **Overlap**: Higher overlap provides better context but increases total text volume
- **Language Parsing**: Tree-sitter parsing adds overhead but provides better semantic splitting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Acknowledgments

This is a TypeScript port of the recursive text splitter from the [cocoindex](https://github.com/cocoindex/cocoindex) project. Thanks to the original authors for the excellent design and implementation.
