# TypeScript Recursive Text Splitter

A TypeScript port of the recursive text splitter from the cocoindex project. This library provides intelligent text chunking with support for multiple programming languages through Tree-sitter parsing.

## Features

- **Recursive Text Splitting**: Intelligently splits text into chunks of specified sizes with configurable overlap
- **Multi-language Support**: Syntax-aware splitting for various programming languages using Tree-sitter
- **Unicode Support**: Proper handling of Unicode characters and emoji
- **Configurable**: Customizable chunk size, overlap, and language-specific parsing
- **Type-safe**: Full TypeScript support with comprehensive type definitions

## Installation

```bash
npm install cocoindex-split-recursively-ts
```

For Tree-sitter language support, install the optional language packages:

```bash
npm install tree-sitter tree-sitter-javascript tree-sitter-python tree-sitter-rust
# ... other language packages as needed
```

## Quick Start

```typescript
import { Factory } from './src/split-recursively';
import { RecursiveChunker } from './src/recursive-chunker';

// Basic text splitting
const text = "This is a long document that needs to be split into smaller chunks for processing.";
const chunker = new RecursiveChunker(text, null, 50, 10);

const result = chunker.splitRootChunk({
  type: 'regexpSepChunk',
  nextRegexpSepId: 0
});

if (result.success) {
  const chunks = result.data;
  chunks.forEach((chunk, index) => {
    console.log(`Chunk ${index}: "${chunk[1]}"`);
  });
}
```

## Advanced Usage

### Language-specific Splitting

```typescript
import { getLanguageConfig } from './src/tree-sitter-config';

const code = `
function hello() {
  console.log("Hello, world!");
}

function goodbye() {
  console.log("Goodbye!");
}
`;

// Get language configuration for JavaScript
const langConfig = await getLanguageConfig('javascript');
const chunker = new RecursiveChunker(code, langConfig, 100, 20);

const result = chunker.splitRootChunk({
  type: 'treeSitterNode',
  node: rootNode // Tree-sitter parsed node
});
```

### Using the Factory Pattern

```typescript
import { Factory } from './src/split-recursively';

const factory = new Factory();

// Create operation arguments
const args = {
  text: { name: 'text', typ: { /* ... */ }, idx: 0 },
  chunkSize: { name: 'chunk_size', typ: { /* ... */ }, idx: 1 },
  chunkOverlap: null,
  language: null
};

// Build executor
const executor = await factory.buildExecutor({}, args, context);

if (executor.success) {
  const result = await executor.data.evaluate([
    { type: 'basic', value: { type: 'str', value: text } },
    { type: 'basic', value: { type: 'int64', value: 100 } }
  ]);
}
```

## Supported Languages

The library supports syntax-aware splitting for the following languages:

- C/C++
- C#
- CSS
- Go
- HTML
- Java
- JavaScript
- JSON
- Markdown
- PHP
- Python
- Ruby
- Rust
- TypeScript/TSX
- XML
- YAML

## API Reference

### RecursiveChunker

The main class for text splitting.

```typescript
class RecursiveChunker {
  constructor(
    fullText: string,
    langConfig: LanguageConfig | null,
    chunkSize: number,
    chunkOverlap: number
  )
  
  splitRootChunk(kind: ChunkKind): Result<Array<[RangeValue, string]>>
}
```

### ChunkKind

Defines how text should be split:

```typescript
type ChunkKind = 
  | { type: 'treeSitterNode'; node: any }
  | { type: 'regexpSepChunk'; nextRegexpSepId: number }
  | { type: 'leafText' };
```

### RangeValue

Represents a text range:

```typescript
class RangeValue {
  constructor(start: number, end: number)
  get length(): number
  extractStr(text: string): string
  toTuple(): [number, number]
  static fromTuple([start, end]: [number, number]): RangeValue
}
```

## Configuration

### Text Separators

The library uses a hierarchy of text separators:

1. Double newlines and more (`\n\n+`)
2. Single newlines (`\n`)
3. Whitespace (`\s+`)

### Language Configuration

Each supported language has:

- **Name**: Human-readable language name
- **Aliases**: File extensions and alternative names
- **Tree-sitter Language**: Parser for syntax-aware splitting
- **Terminal Node Kinds**: Node types that should not be split further

## Testing

Run the test suite:

```bash
npm test
```

Run tests in development mode:

```bash
npm run dev
```

## Building

Compile TypeScript to JavaScript:

```bash
npm run build
```

## Performance Considerations

- **Chunk Size**: Larger chunks reduce processing overhead but may impact downstream processing
- **Overlap**: Higher overlap provides better context but increases total text volume
- **Language Parsing**: Tree-sitter parsing adds overhead but provides better semantic splitting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Acknowledgments

This is a TypeScript port of the recursive text splitter from the [cocoindex](https://github.com/cocoindex/cocoindex) project. Thanks to the original authors for the excellent design and implementation.
