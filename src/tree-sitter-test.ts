/**
 * Test Tree-sitter integration with the recursive text splitter
 */

import { RecursiveChunker } from './recursive-chunker';
import { getLanguageConfig, createParser } from './tree-sitter-config';

/**
 * Test Tree-sitter JavaScript parsing
 */
async function testTreeSitterJavaScript() {
  console.log('=== Tree-sitter JavaScript Test ===');
  
  const jsCode = `
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

function factorial(n) {
  if (n <= 1) return 1;
  return n * factorial(n - 1);
}

class Calculator {
  constructor() {
    this.history = [];
  }
  
  add(a, b) {
    const result = a + b;
    this.history.push({ operation: 'add', a, b, result });
    return result;
  }
  
  multiply(a, b) {
    const result = a * b;
    this.history.push({ operation: 'multiply', a, b, result });
    return result;
  }
}

export { fibonacci, factorial, Calculator };
  `.trim();

  try {
    // Get JavaScript language configuration
    const langConfig = await getLanguageConfig('javascript');
    
    if (!langConfig) {
      console.log('❌ Tree-sitter JavaScript not available');
      return false;
    }
    
    console.log(`✓ Loaded Tree-sitter language: ${langConfig.name}`);
    
    // Create parser
    const parser = createParser(langConfig);
    if (!parser) {
      console.log('❌ Failed to create Tree-sitter parser');
      return false;
    }
    
    console.log('✓ Created Tree-sitter parser');
    
    // Parse the code
    const tree = parser.parse(jsCode);
    if (!tree || !tree.rootNode) {
      console.log('❌ Failed to parse JavaScript code');
      return false;
    }
    
    console.log('✓ Parsed JavaScript code successfully');
    console.log(`Root node type: ${tree.rootNode.type}`);
    console.log(`Child count: ${tree.rootNode.childCount}`);
    
    // Test with RecursiveChunker
    const chunker = new RecursiveChunker(jsCode, langConfig, 200, 30);
    
    const result = chunker.splitRootChunk({
      type: 'treeSitterNode',
      node: tree.rootNode
    });
    
    if (!result.success) {
      console.log('❌ Failed to split code:', result.error);
      return false;
    }
    
    const chunks = result.data;
    console.log(`✓ Split into ${chunks.length} chunks using Tree-sitter`);
    
    chunks.forEach((chunk, index) => {
      console.log(`\nChunk ${index + 1} (${chunk[0].start}-${chunk[0].end}):`);
      console.log(`"${chunk[1]}"`);
    });
    
    return true;
    
  } catch (error) {
    console.log('❌ Error in Tree-sitter test:', error);
    return false;
  }
}

/**
 * Compare Tree-sitter vs Regexp splitting
 */
async function compareTreeSitterVsRegexp() {
  console.log('\n=== Tree-sitter vs Regexp Comparison ===');
  
  const jsCode = `
function hello() {
  console.log("Hello, world!");
}

function goodbye() {
  console.log("Goodbye!");
}
  `.trim();

  try {
    const langConfig = await getLanguageConfig('javascript');
    
    // Test with Tree-sitter
    let treeSitterChunks: Array<[any, string]> = [];
    if (langConfig) {
      const chunker = new RecursiveChunker(jsCode, langConfig, 100, 20);
      const parser = createParser(langConfig);
      
      if (parser) {
        const tree = parser.parse(jsCode);
        if (tree && tree.rootNode) {
          const result = chunker.splitRootChunk({
            type: 'treeSitterNode',
            node: tree.rootNode
          });
          
          if (result.success) {
            treeSitterChunks = result.data;
          }
        }
      }
    }
    
    // Test with Regexp
    const regexpChunker = new RecursiveChunker(jsCode, null, 100, 20);
    const regexpResult = regexpChunker.splitRootChunk({
      type: 'regexpSepChunk',
      nextRegexpSepId: 0
    });
    
    const regexpChunks = regexpResult.success ? regexpResult.data : [];
    
    console.log(`Tree-sitter chunks: ${treeSitterChunks.length}`);
    treeSitterChunks.forEach((chunk, i) => {
      console.log(`  ${i + 1}: "${chunk[1]}"`);
    });
    
    console.log(`\nRegexp chunks: ${regexpChunks.length}`);
    regexpChunks.forEach((chunk, i) => {
      console.log(`  ${i + 1}: "${chunk[1]}"`);
    });
    
    return true;
    
  } catch (error) {
    console.log('❌ Error in comparison test:', error);
    return false;
  }
}

/**
 * Test Tree-sitter node inspection
 */
async function testTreeSitterNodeInspection() {
  console.log('\n=== Tree-sitter Node Inspection ===');
  
  const simpleCode = 'function test() { return 42; }';
  
  try {
    const langConfig = await getLanguageConfig('javascript');
    if (!langConfig) {
      console.log('❌ Tree-sitter JavaScript not available');
      return false;
    }
    
    const parser = createParser(langConfig);
    if (!parser) {
      console.log('❌ Failed to create parser');
      return false;
    }
    
    const tree = parser.parse(simpleCode);
    if (!tree || !tree.rootNode) {
      console.log('❌ Failed to parse code');
      return false;
    }
    
    // Inspect the syntax tree
    function inspectNode(node: any, depth = 0): void {
      const indent = '  '.repeat(depth);
      console.log(`${indent}${node.type} [${node.startIndex}-${node.endIndex}]: "${simpleCode.slice(node.startIndex, node.endIndex)}"`);
      
      if (node.children) {
        for (const child of node.children) {
          inspectNode(child, depth + 1);
        }
      }
    }
    
    console.log('Syntax tree:');
    inspectNode(tree.rootNode);
    
    return true;
    
  } catch (error) {
    console.log('❌ Error in node inspection:', error);
    return false;
  }
}

/**
 * Run all Tree-sitter tests
 */
async function runTreeSitterTests() {
  console.log('Tree-sitter Integration Tests\n');
  
  const tests = [
    testTreeSitterJavaScript,
    compareTreeSitterVsRegexp,
    testTreeSitterNodeInspection
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    try {
      const result = await test();
      if (result) {
        passed++;
      }
    } catch (error) {
      console.log('❌ Test failed with error:', error);
    }
  }
  
  console.log(`\n📊 Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('✅ All Tree-sitter tests passed!');
  } else {
    console.log('⚠️  Some Tree-sitter tests failed or were skipped');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTreeSitterTests();
}

export {
  testTreeSitterJavaScript,
  compareTreeSitterVsRegexp,
  testTreeSitterNodeInspection,
  runTreeSitterTests
};
