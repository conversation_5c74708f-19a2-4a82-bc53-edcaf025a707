/**
 * Test suite for the TypeScript port of split_recursively
 * Ported from the Rust test cases
 */

import { RangeValue } from './types';
import { RecursiveChunker, translateBytesToChars } from './recursive-chunker';

/**
 * Helper function to assert chunk text and its consistency with the range within the original text
 */
function assertChunkTextConsistency(
  fullText: string,
  actualChunk: [RangeValue, string],
  expectedText: string,
  context: string
): void {
  const [range, chunkText] = actualChunk;

  // Extract text using the chunk's range from the original full text
  const extractedText = range.extractStr(fullText);

  // Assert that the expected text matches the text provided in the chunk
  if (chunkText !== expectedText) {
    throw new Error(`Provided chunk text mismatch - ${context}: expected "${expectedText}", got "${chunkText}"`);
  }

  // Assert that the expected text also matches the text extracted using the chunk's range
  if (extractedText !== expectedText) {
    throw new Error(`Range inconsistency: extracted text mismatch - ${context}: expected "${expectedText}", got "${extractedText}"`);
  }
}

/**
 * Creates a default RecursiveChunker for testing, assuming no language-specific parsing
 */
function createTestChunker(
  text: string,
  chunkSize: number,
  chunkOverlap: number
): RecursiveChunker {
  return new RecursiveChunker(text, null, chunkSize, chunkOverlap);
}

/**
 * Test translate bytes to chars function
 */
function testTranslateBytesToCharsSimple(): void {
  const text = "abc😄def";
  const offsets = [0, 3, 3, 7, 7, 10, text.length];

  translateBytesToChars(text, offsets);

  // Expected character positions
  const expected = [0, 3, 3, 4, 4, 7, 7];

  for (let i = 0; i < offsets.length; i++) {
    if (offsets[i] !== expected[i]) {
      throw new Error(`Offset ${i}: expected ${expected[i]}, got ${offsets[i]}`);
    }
  }

  console.log('✓ testTranslateBytesToCharsSimple passed');
}

/**
 * Test basic split with no overlap
 */
function testBasicSplitNoOverlap(): void {
  const text = "Linea 1.\nLinea 2.\n\nLinea 3.";
  const chunker = createTestChunker(text, 15, 0);

  const result = chunker.splitRootChunk({
    type: 'regexpSepChunk',
    nextRegexpSepId: 0
  });

  if (!result.success) {
    throw new Error(`Split failed: ${result.error}`);
  }

  const chunks = result.data;

  if (chunks.length !== 3) {
    throw new Error(`Expected 3 chunks, got ${chunks.length}`);
  }

  assertChunkTextConsistency(text, chunks[0], "Linea 1.", "Test 1, Chunk 0");
  assertChunkTextConsistency(text, chunks[1], "Linea 2.", "Test 1, Chunk 1");
  assertChunkTextConsistency(text, chunks[2], "Linea 3.", "Test 1, Chunk 2");

  // Test splitting when chunk_size forces breaks within segments
  const text2 = "A very very long text that needs to be split.";
  const chunker2 = createTestChunker(text2, 20, 0);
  const result2 = chunker2.splitRootChunk({
    type: 'regexpSepChunk',
    nextRegexpSepId: 0
  });

  if (!result2.success) {
    throw new Error(`Split failed: ${result2.error}`);
  }

  const chunks2 = result2.data;

  // Expect multiple chunks, likely split by spaces due to chunk_size
  if (chunks2.length <= 1) {
    throw new Error(`Expected multiple chunks, got ${chunks2.length}`);
  }

  assertChunkTextConsistency(text2, chunks2[0], "A very very long", "Test 2, Chunk 0");

  if (chunks2[0][1].length > 20) {
    throw new Error(`Chunk too long: ${chunks2[0][1].length} > 20`);
  }

  console.log('✓ testBasicSplitNoOverlap passed');
}

/**
 * Test basic split with overlap
 */
function testBasicSplitWithOverlap(): void {
  const text = "This is a test text that is a bit longer to see how the overlap works.";
  const chunker = createTestChunker(text, 20, 5);

  const result = chunker.splitRootChunk({
    type: 'regexpSepChunk',
    nextRegexpSepId: 0
  });

  if (!result.success) {
    throw new Error(`Split failed: ${result.error}`);
  }

  const chunks = result.data;

  if (chunks.length <= 1) {
    throw new Error(`Expected multiple chunks, got ${chunks.length}`);
  }

  if (chunks.length >= 2) {
    if (chunks[0][1].length > 25) {
      throw new Error(`First chunk too long: ${chunks[0][1].length} > 25`);
    }
  }

  console.log('✓ testBasicSplitWithOverlap passed');
}

/**
 * Test that split trims whitespace
 */
function testSplitTrimsWhitespace(): void {
  const text = "  \n First chunk. \n\n  Second chunk with spaces at the end.   \n";
  const chunker = createTestChunker(text, 30, 0);

  const result = chunker.splitRootChunk({
    type: 'regexpSepChunk',
    nextRegexpSepId: 0
  });

  if (!result.success) {
    throw new Error(`Split failed: ${result.error}`);
  }

  const chunks = result.data;

  if (chunks.length !== 3) {
    throw new Error(`Expected 3 chunks, got ${chunks.length}`);
  }

  assertChunkTextConsistency(text, chunks[0], "First chunk.", "Whitespace Test, Chunk 0");
  assertChunkTextConsistency(text, chunks[1], "Second chunk with spaces at", "Whitespace Test, Chunk 1");
  assertChunkTextConsistency(text, chunks[2], "the end.", "Whitespace Test, Chunk 2");

  console.log('✓ testSplitTrimsWhitespace passed');
}

/**
 * Test that split discards empty chunks
 */
function testSplitDiscardsEmptyChunks(): void {
  const text = "Chunk 1.\n\n   \n\nChunk 2.\n\n------\n\nChunk 3.";
  const chunker = createTestChunker(text, 10, 0);

  const result = chunker.splitRootChunk({
    type: 'regexpSepChunk',
    nextRegexpSepId: 0
  });

  if (!result.success) {
    throw new Error(`Split failed: ${result.error}`);
  }

  const chunks = result.data;

  if (chunks.length !== 3) {
    throw new Error(`Expected 3 chunks, got ${chunks.length}`);
  }

  // Expect only the chunks with actual alphanumeric content
  assertChunkTextConsistency(text, chunks[0], "Chunk 1.", "Discard Test, Chunk 0");
  assertChunkTextConsistency(text, chunks[1], "Chunk 2.", "Discard Test, Chunk 1");
  assertChunkTextConsistency(text, chunks[2], "Chunk 3.", "Discard Test, Chunk 2");

  console.log('✓ testSplitDiscardsEmptyChunks passed');
}

/**
 * Test RangeValue functionality
 */
function testRangeValue(): void {
  const range = new RangeValue(5, 10);

  if (range.length !== 5) {
    throw new Error(`Expected length 5, got ${range.length}`);
  }

  const text = "Hello World!";
  const extracted = range.extractStr(text);
  if (extracted !== " Worl") {
    throw new Error(`Expected " Worl", got "${extracted}"`);
  }

  const tuple = range.toTuple();
  if (tuple[0] !== 5 || tuple[1] !== 10) {
    throw new Error(`Expected [5, 10], got [${tuple[0]}, ${tuple[1]}]`);
  }

  const fromTuple = RangeValue.fromTuple([3, 8]);
  if (fromTuple.start !== 3 || fromTuple.end !== 8) {
    throw new Error(`Expected start=3, end=8, got start=${fromTuple.start}, end=${fromTuple.end}`);
  }

  console.log('✓ testRangeValue passed');
}

/**
 * Run all tests
 */
function runAllTests(): void {
  console.log('Running TypeScript split_recursively tests...\n');

  try {
    testRangeValue();
    testTranslateBytesToCharsSimple();
    testBasicSplitNoOverlap();
    testBasicSplitWithOverlap();
    testSplitTrimsWhitespace();
    testSplitDiscardsEmptyChunks();

    console.log('\n✅ All tests passed!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

export {
  runAllTests,
  testRangeValue,
  testTranslateBytesToCharsSimple,
  testBasicSplitNoOverlap,
  testBasicSplitWithOverlap,
  testSplitTrimsWhitespace,
  testSplitDiscardsEmptyChunks
};
