/**
 * TypeScript port of split_recursively.rs from cocoindex
 * Recursive text splitting with Tree-sitter support
 */

import {
  Value,
  RangeValue,
  ResolvedOpArg,
  EmptySpec,
  FlowInstanceContext,
  EnrichedValueType,
  BasicValueType,
  StructSchema,
  TableSchema,
  Result,
  Ok,
  Err,
  makeOutputType,
  createKTableValue,
  fieldsValue,
  createStrValue,
  createRangeValue
} from './types';

import {
  SimpleFunctionExecutor,
  SimpleFunctionFactoryBase,
  OpArgsResolver,
  ResolvedOpArgExt,
  OptionalResolvedOpArgExt,
  ValueHelpers,
  FIELD_ATTRS
} from './interfaces';

import {
  RecursiveChunker,
  translateBytesToChars
} from './recursive-chunker';

import { getLanguageConfig, createParser } from './tree-sitter-config';

/**
 * Arguments for the SplitRecursively operation
 */
interface Args {
  text: ResolvedOpArg;
  chunkSize: ResolvedOpArg;
  chunkOverlap: ResolvedOpArg | null;
  language: ResolvedOpArg | null;
}

/**
 * Executor for the SplitRecursively operation
 */
class Executor implements SimpleFunctionExecutor {
  private args: Args;

  constructor(args: Args) {
    this.args = args;
  }

  async evaluate(input: Value[]): Promise<Result<Value>> {
    try {
      // Extract arguments
      const textResult = ResolvedOpArgExt.getValue(this.args.text, input);
      if (!textResult.success) return textResult;

      const textStrResult = ValueHelpers.asStr(textResult.data);
      if (!textStrResult.success) return textStrResult;
      const fullText = textStrResult.data;

      const chunkSizeResult = ResolvedOpArgExt.getValue(this.args.chunkSize, input);
      if (!chunkSizeResult.success) return chunkSizeResult;

      const chunkSizeIntResult = ValueHelpers.asInt64(chunkSizeResult.data);
      if (!chunkSizeIntResult.success) return chunkSizeIntResult;
      const chunkSize = chunkSizeIntResult.data;

      // Handle optional chunk overlap
      const chunkOverlapResult = OptionalResolvedOpArgExt.getValue(this.args.chunkOverlap, input);
      if (!chunkOverlapResult.success) return chunkOverlapResult;

      let chunkOverlap = 0;
      const chunkOverlapValue = ValueHelpers.optional(chunkOverlapResult.data);
      if (chunkOverlapValue) {
        const chunkOverlapIntResult = ValueHelpers.asInt64(chunkOverlapValue);
        if (!chunkOverlapIntResult.success) return chunkOverlapIntResult;
        chunkOverlap = chunkOverlapIntResult.data;
      }

      // Handle optional language (matching Rust implementation)
      const languageResult = OptionalResolvedOpArgExt.getValue(this.args.language, input);
      if (!languageResult.success) return languageResult;

      let langConfig = null;
      const languageValue = ValueHelpers.optional(languageResult.data);
      if (languageValue) {
        const languageStrResult = ValueHelpers.asStr(languageValue);
        if (!languageStrResult.success) return languageStrResult;

        // Use the static language mapping table (equivalent to TREE_SITTER_LANGUAGE_BY_LANG.get())
        langConfig = await getLanguageConfig(languageStrResult.data);
      }

      // Create recursive chunker
      const recursiveChunker = new RecursiveChunker(
        fullText,
        langConfig,
        chunkSize,
        chunkOverlap
      );

      // Split the text
      let outputResult: Result<Array<[RangeValue, string]>>;

      if (langConfig) {
        // Use Tree-sitter parsing
        try {
          const parser = createParser(langConfig);
          if (parser) {
            const tree = parser.parse(fullText);
            if (tree && tree.rootNode) {
              outputResult = recursiveChunker.splitRootChunk({
                type: 'treeSitterNode',
                node: tree.rootNode
              });
            } else {
              console.warn(`Failed to parse text with ${langConfig.name}, falling back to regexp splitting`);
              outputResult = recursiveChunker.splitRootChunk({
                type: 'regexpSepChunk',
                nextRegexpSepId: 0
              });
            }
          } else {
            console.warn(`Failed to create parser for ${langConfig.name}, falling back to regexp splitting`);
            outputResult = recursiveChunker.splitRootChunk({
              type: 'regexpSepChunk',
              nextRegexpSepId: 0
            });
          }
        } catch (error) {
          console.warn(`Error parsing text with ${langConfig.name}:`, error);
          outputResult = recursiveChunker.splitRootChunk({
            type: 'regexpSepChunk',
            nextRegexpSepId: 0
          });
        }
      } else {
        // Use regexp-based splitting
        outputResult = recursiveChunker.splitRootChunk({
          type: 'regexpSepChunk',
          nextRegexpSepId: 0
        });
      }

      if (!outputResult.success) {
        return outputResult;
      }

      let output = outputResult.data;

      // Translate byte positions to character positions
      const allOffsets: number[] = [];
      output.forEach(([range, _]) => {
        allOffsets.push(range.start, range.end);
      });
      translateBytesToChars(fullText, allOffsets);

      // Update ranges with translated positions
      let offsetIndex = 0;
      output = output.map(([_range, text]) => {
        const newStart = allOffsets[offsetIndex++];
        const newEnd = allOffsets[offsetIndex++];
        return [new RangeValue(newStart, newEnd), text];
      });

      // Convert to KTable format
      const tableEntries: Array<[any, Value]> = output.map(([range, text]) => {
        const key = { type: 'range', value: range };
        const value = fieldsValue(
          createRangeValue(range.start, range.end),
          createStrValue(text)
        );
        return [key, { type: 'struct', value }];
      });

      const result = createKTableValue(tableEntries);
      return Ok(result);

    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  enableCache(): boolean {
    return false;
  }

  behaviorVersion(): number | null {
    return null;
  }
}

/**
 * Factory for the SplitRecursively operation
 */
export class Factory implements SimpleFunctionFactoryBase<EmptySpec, Args> {
  name(): string {
    return 'SplitRecursively';
  }

  resolveSchema(
    _spec: EmptySpec,
    argsResolver: OpArgsResolver,
    _context: FlowInstanceContext
  ): Result<{ resolvedArgs: Args; outputSchema: EnrichedValueType }> {
    try {
      // Resolve text argument
      const textResult = argsResolver.nextArg('text');
      if (!textResult.success) return textResult;

      const textArgResult = ResolvedOpArgExt.expectType(textResult.data, {
        type: 'basic',
        basicType: BasicValueType.Str
      });
      if (!textArgResult.success) return textArgResult;

      // Resolve chunk_size argument
      const chunkSizeResult = argsResolver.nextArg('chunk_size');
      if (!chunkSizeResult.success) return chunkSizeResult;

      const chunkSizeArgResult = ResolvedOpArgExt.expectType(chunkSizeResult.data, {
        type: 'basic',
        basicType: BasicValueType.Int64
      });
      if (!chunkSizeArgResult.success) return chunkSizeArgResult;

      // Resolve optional chunk_overlap argument
      const chunkOverlapResult = argsResolver.nextOptionalArg('chunk_overlap');
      if (!chunkOverlapResult.success) return chunkOverlapResult;

      let chunkOverlapArg = null;
      if (chunkOverlapResult.data) {
        const chunkOverlapArgResult = ResolvedOpArgExt.expectType(chunkOverlapResult.data, {
          type: 'basic',
          basicType: BasicValueType.Int64
        });
        if (!chunkOverlapArgResult.success) return chunkOverlapArgResult;
        chunkOverlapArg = chunkOverlapArgResult.data;
      }

      // Resolve optional language argument
      const languageResult = argsResolver.nextOptionalArg('language');
      if (!languageResult.success) return languageResult;

      let languageArg = null;
      if (languageResult.data) {
        const languageArgResult = ResolvedOpArgExt.expectType(languageResult.data, {
          type: 'basic',
          basicType: BasicValueType.Str
        });
        if (!languageArgResult.success) return languageArgResult;
        languageArg = languageArgResult.data;
      }

      const args: Args = {
        text: textArgResult.data,
        chunkSize: chunkSizeArgResult.data,
        chunkOverlap: chunkOverlapArg,
        language: languageArg
      };

      // Create output schema
      const structSchema: StructSchema = {
        fields: [
          {
            name: 'location',
            valueType: makeOutputType({ type: 'basic', basicType: BasicValueType.Range })
          },
          {
            name: 'text',
            valueType: makeOutputType({ type: 'basic', basicType: BasicValueType.Str })
          }
        ]
      };

      const tableSchema: TableSchema = {
        kind: 'KTable',
        schema: structSchema
      };

      const outputSchema = makeOutputType({ type: 'table', schema: tableSchema });

      // Add chunk base text attribute
      outputSchema.attrs[FIELD_ATTRS.CHUNK_BASE_TEXT] = argsResolver.getAnalyzeValue(args.text);

      const doneResult = argsResolver.done();
      if (!doneResult.success) return doneResult;

      return Ok({ resolvedArgs: args, outputSchema });

    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  async buildExecutor(
    _spec: EmptySpec,
    args: Args,
    _context: FlowInstanceContext
  ): Promise<Result<SimpleFunctionExecutor>> {
    try {
      return Ok(new Executor(args));
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  build(
    spec: any,
    inputSchema: any[],
    context: FlowInstanceContext
  ): Result<{
    outputSchema: EnrichedValueType;
    executor: Promise<Result<SimpleFunctionExecutor>>;
  }> {
    const argsResolver = new OpArgsResolver(inputSchema);
    const resolveResult = this.resolveSchema(spec, argsResolver, context);

    if (!resolveResult.success) {
      return resolveResult;
    }

    const { resolvedArgs, outputSchema } = resolveResult.data;
    const executor = this.buildExecutor(spec, resolvedArgs, context);

    return Ok({ outputSchema, executor });
  }
}
