/**
 * Tree-sitter language configurations for syntax-aware text splitting
 * This file contains the language configurations for various programming languages
 */

import { LanguageConfig } from './recursive-chunker';

// Import Tree-sitter core
let Parser: any;
try {
  Parser = require('tree-sitter');
} catch (error) {
  console.warn('Tree-sitter not available:', error);
}

/**
 * Language configuration with lazy loading support
 */
interface LazyLanguageConfig {
  name: string;
  aliases: string[];
  loader: () => Promise<any>; // Tree-sitter language loader
  terminalNodeKinds: string[];
}

/**
 * Static language mapping table (equivalent to Rust's TREE_SITTER_LANGUAGE_BY_LANG)
 * This is initialized once and cached for performance
 */
const TREE_SITTER_LANGUAGE_BY_LANG = new Map<string, LanguageConfig>();

/**
 * Helper function to add a language to the mapping table
 */
function addLanguage(
  map: Map<string, LanguageConfig>,
  name: string,
  aliases: string[],
  loader: () => any,
  terminalNodeKinds: string[] = []
): void {
  const config: LanguageConfig = {
    name,
    treeSitterLang: null, // Will be loaded lazily
    terminalNodeKindIds: new Set()
  };

  // Add primary name (case-insensitive)
  const normalizedName = name.toLowerCase();
  if (map.has(normalizedName)) {
    throw new Error(`Language '${name}' already exists`);
  }
  map.set(normalizedName, config);

  // Add aliases (case-insensitive)
  for (const alias of aliases) {
    const normalizedAlias = alias.toLowerCase();
    if (map.has(normalizedAlias)) {
      throw new Error(`Language alias '${alias}' already exists`);
    }
    map.set(normalizedAlias, config);
  }

  // Store loader and terminal node kinds for later initialization
  (config as any)._loader = loader;
  (config as any)._terminalNodeKinds = terminalNodeKinds;
}

/**
 * Initialize the language mapping table (equivalent to Rust's LazyLock initialization)
 */
function initializeLanguageMap(): void {
  if (TREE_SITTER_LANGUAGE_BY_LANG.size > 0) {
    return; // Already initialized
  }

  // Add all supported languages (matching the Rust implementation exactly)
  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "C", [".c"], () => {
    try { return require('tree-sitter-c'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "C++", [".cpp", ".cc", ".cxx", ".h", ".hpp", "cpp"], () => {
    try { return require('tree-sitter-cpp'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "C#", [".cs", "cs"], () => {
    try { return require('tree-sitter-c-sharp'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "CSS", [".css", ".scss"], () => {
    try { return require('tree-sitter-css'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Fortran", [".f", ".f90", ".f95", ".f03", "f", "f90", "f95", "f03"], () => {
    try { return require('tree-sitter-fortran'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Go", [".go", "golang"], () => {
    try { return require('tree-sitter-go'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "HTML", [".html", ".htm"], () => {
    try { return require('tree-sitter-html'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Java", [".java"], () => {
    try { return require('tree-sitter-java'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "JavaScript", [".js", "js"], () => {
    try { return require('tree-sitter-javascript'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "JSON", [".json"], () => {
    try { return require('tree-sitter-json'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Markdown", [".md", ".mdx", "md"], () => {
    try { return require('tree-sitter-markdown'); } catch { return null; }
  }, ["inline"]);

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Pascal", [".pas", "pas", ".dpr", "dpr", "Delphi"], () => {
    try { return require('tree-sitter-pascal'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "PHP", [".php"], () => {
    try {
      const php = require('tree-sitter-php');
      return php.LANGUAGE_PHP || php;
    } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Python", [".py"], () => {
    try { return require('tree-sitter-python'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "R", [".r"], () => {
    try { return require('tree-sitter-r'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Ruby", [".rb"], () => {
    try { return require('tree-sitter-ruby'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Rust", [".rs", "rs"], () => {
    try { return require('tree-sitter-rust'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Scala", [".scala"], () => {
    try { return require('tree-sitter-scala'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "SQL", [".sql"], () => {
    try { return require('tree-sitter-sql'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "Swift", [".swift"], () => {
    try { return require('tree-sitter-swift'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "TOML", [".toml"], () => {
    try { return require('tree-sitter-toml'); } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "TSX", [".tsx"], () => {
    try {
      const ts = require('tree-sitter-typescript');
      return ts.LANGUAGE_TSX || ts.tsx;
    } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "TypeScript", [".ts", "ts"], () => {
    try {
      const ts = require('tree-sitter-typescript');
      return ts.LANGUAGE_TYPESCRIPT || ts.typescript;
    } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "XML", [".xml"], () => {
    try {
      const xml = require('tree-sitter-xml');
      return xml.LANGUAGE_XML || xml;
    } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "DTD", [".dtd"], () => {
    try {
      const xml = require('tree-sitter-xml');
      return xml.LANGUAGE_DTD || xml;
    } catch { return null; }
  });

  addLanguage(TREE_SITTER_LANGUAGE_BY_LANG, "YAML", [".yaml", ".yml"], () => {
    try { return require('tree-sitter-yaml'); } catch { return null; }
  });
}

/**
 * Language configurations for supported programming languages
 * Note: This is now replaced by the static mapping table above
 * Keeping for backward compatibility
 */
const LANGUAGE_CONFIGS: LazyLanguageConfig[] = [
  {
    name: 'C',
    aliases: ['.c', 'c'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-c');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'C++',
    aliases: ['.cpp', '.cc', '.cxx', '.h', '.hpp', 'cpp'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-cpp');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'C#',
    aliases: ['.cs', 'cs'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-csharp');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'CSS',
    aliases: ['.css', '.scss'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-css');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Go',
    aliases: ['.go', 'golang'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-go');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'HTML',
    aliases: ['.html', '.htm'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-html');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Java',
    aliases: ['.java'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-java');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'JavaScript',
    aliases: ['.js', 'js'],
    loader: async () => {
      try {
        return require('tree-sitter-javascript');
      } catch (error) {
        console.warn('tree-sitter-javascript not available:', error);
        return null;
      }
    },
    terminalNodeKinds: []
  },
  {
    name: 'JSON',
    aliases: ['.json'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-json');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Markdown',
    aliases: ['.md', '.mdx', 'md'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-markdown');
      return null; // Placeholder
    },
    terminalNodeKinds: ['inline']
  },
  {
    name: 'PHP',
    aliases: ['.php'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-php');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Python',
    aliases: ['.py'],
    loader: async () => {
      try {
        return require('tree-sitter-python');
      } catch (error) {
        console.warn('tree-sitter-python not available:', error);
        return null;
      }
    },
    terminalNodeKinds: []
  },
  {
    name: 'Ruby',
    aliases: ['.rb'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-ruby');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Rust',
    aliases: ['.rs', 'rs'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-rust');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'TypeScript',
    aliases: ['.ts', 'ts'],
    loader: async () => {
      try {
        const tsModule = require('tree-sitter-typescript');
        return tsModule.typescript;
      } catch (error) {
        console.warn('tree-sitter-typescript not available:', error);
        return null;
      }
    },
    terminalNodeKinds: []
  },
  {
    name: 'TSX',
    aliases: ['.tsx'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-typescript').then(m => m.tsx);
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'XML',
    aliases: ['.xml'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-xml');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'YAML',
    aliases: ['.yaml', '.yml'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-yaml');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  }
];

/**
 * Legacy language configurations (kept for backward compatibility)
 * The static mapping table above is now the primary source of truth
 */

/**
 * Get language configuration by name or file extension (using static mapping table)
 */
export async function getLanguageConfig(language: string): Promise<LanguageConfig | null> {
  // Initialize the language map if not already done
  initializeLanguageMap();

  const key = language.toLowerCase();

  // Check if Tree-sitter is available
  if (!Parser) {
    console.warn('Tree-sitter not available');
    return null;
  }

  // Find language configuration in the static mapping table
  const config = TREE_SITTER_LANGUAGE_BY_LANG.get(key);
  if (!config) {
    return null;
  }

  // Check if already loaded
  if (config.treeSitterLang) {
    return config;
  }

  try {
    // Load the tree-sitter language using the stored loader
    const loader = (config as any)._loader;
    const terminalNodeKinds = (config as any)._terminalNodeKinds || [];

    if (!loader) {
      console.warn(`No loader found for language ${config.name}`);
      return null;
    }

    const treeSitterLang = loader();
    if (!treeSitterLang) {
      console.warn(`Tree-sitter language not available for ${config.name}`);
      return null;
    }

    // Get terminal node kind IDs
    const terminalNodeKindIds = new Set<number>();
    for (const kind of terminalNodeKinds) {
      try {
        const id = treeSitterLang.idForNodeKind ? treeSitterLang.idForNodeKind(kind, true) : 0;
        if (id !== 0) {
          terminalNodeKindIds.add(id);
          console.debug(`Got id for node kind: \`${kind}\` -> ${id}`);
        } else {
          console.warn(`Failed to get id for node kind: \`${kind}\``);
        }
      } catch (error) {
        console.warn(`Error getting node kind id for ${kind}:`, error);
      }
    }

    // Update the config with loaded language
    config.treeSitterLang = treeSitterLang;
    config.terminalNodeKindIds = terminalNodeKindIds;

    return config;
  } catch (error) {
    console.error(`Failed to load tree-sitter language for ${config.name}:`, error);
    return null;
  }
}

/**
 * Create a Tree-sitter parser for a given language
 */
export function createParser(langConfig: LanguageConfig): any | null {
  if (!Parser) {
    console.warn('Tree-sitter not available');
    return null;
  }

  try {
    const parser = new Parser();
    parser.setLanguage(langConfig.treeSitterLang);
    return parser;
  } catch (error) {
    console.error(`Failed to create parser for ${langConfig.name}:`, error);
    return null;
  }
}

/**
 * Get list of supported languages (using static mapping table)
 */
export function getSupportedLanguages(): string[] {
  initializeLanguageMap();
  const languages = new Set<string>();

  for (const [key, config] of TREE_SITTER_LANGUAGE_BY_LANG) {
    // Only add the primary language name (not aliases)
    if (key === config.name.toLowerCase()) {
      languages.add(config.name);
    }
  }

  return Array.from(languages).sort();
}

/**
 * Get list of supported file extensions (using static mapping table)
 */
export function getSupportedExtensions(): string[] {
  initializeLanguageMap();
  const extensions = new Set<string>();

  for (const key of TREE_SITTER_LANGUAGE_BY_LANG.keys()) {
    if (key.startsWith('.')) {
      extensions.add(key);
    }
  }

  return Array.from(extensions).sort();
}

/**
 * Detect language from file extension (using static mapping table)
 */
export function detectLanguageFromExtension(filename: string): string | null {
  initializeLanguageMap();

  const ext = filename.toLowerCase();
  const dotIndex = ext.lastIndexOf('.');
  if (dotIndex === -1) {
    return null;
  }

  const extension = ext.substring(dotIndex);
  const config = TREE_SITTER_LANGUAGE_BY_LANG.get(extension);
  return config ? config.name : null;
}

/**
 * Check if a language is supported (using static mapping table)
 */
export function isLanguageSupported(language: string): boolean {
  initializeLanguageMap();
  return TREE_SITTER_LANGUAGE_BY_LANG.has(language.toLowerCase());
}

/**
 * Get all language aliases for a given language
 */
export function getLanguageAliases(language: string): string[] {
  initializeLanguageMap();

  const normalizedLang = language.toLowerCase();
  const config = TREE_SITTER_LANGUAGE_BY_LANG.get(normalizedLang);
  if (!config) {
    return [];
  }

  const aliases: string[] = [];
  for (const [key, cfg] of TREE_SITTER_LANGUAGE_BY_LANG) {
    if (cfg === config && key !== normalizedLang) {
      aliases.push(key);
    }
  }

  return aliases.sort();
}
