/**
 * Tree-sitter language configurations for syntax-aware text splitting
 * This file contains the language configurations for various programming languages
 */

import { LanguageConfig } from './recursive-chunker';

// Import Tree-sitter core
let Parser: any;
try {
  Parser = require('tree-sitter');
} catch (error) {
  console.warn('Tree-sitter not available:', error);
}

/**
 * Language configuration with lazy loading support
 */
interface LazyLanguageConfig {
  name: string;
  aliases: string[];
  loader: () => Promise<any>; // Tree-sitter language loader
  terminalNodeKinds: string[];
}

/**
 * Language configurations for supported programming languages
 * Note: In a real implementation, you would install the corresponding npm packages:
 * - tree-sitter
 * - tree-sitter-c
 * - tree-sitter-cpp
 * - tree-sitter-csharp
 * - tree-sitter-css
 * - tree-sitter-go
 * - tree-sitter-html
 * - tree-sitter-java
 * - tree-sitter-javascript
 * - tree-sitter-json
 * - tree-sitter-markdown
 * - tree-sitter-php
 * - tree-sitter-python
 * - tree-sitter-ruby
 * - tree-sitter-rust
 * - tree-sitter-typescript
 * - tree-sitter-xml
 * - tree-sitter-yaml
 * etc.
 */
const LANGUAGE_CONFIGS: LazyLanguageConfig[] = [
  {
    name: 'C',
    aliases: ['.c', 'c'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-c');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'C++',
    aliases: ['.cpp', '.cc', '.cxx', '.h', '.hpp', 'cpp'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-cpp');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'C#',
    aliases: ['.cs', 'cs'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-csharp');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'CSS',
    aliases: ['.css', '.scss'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-css');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Go',
    aliases: ['.go', 'golang'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-go');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'HTML',
    aliases: ['.html', '.htm'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-html');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Java',
    aliases: ['.java'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-java');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'JavaScript',
    aliases: ['.js', 'js'],
    loader: async () => {
      try {
        return require('tree-sitter-javascript');
      } catch (error) {
        console.warn('tree-sitter-javascript not available:', error);
        return null;
      }
    },
    terminalNodeKinds: []
  },
  {
    name: 'JSON',
    aliases: ['.json'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-json');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Markdown',
    aliases: ['.md', '.mdx', 'md'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-markdown');
      return null; // Placeholder
    },
    terminalNodeKinds: ['inline']
  },
  {
    name: 'PHP',
    aliases: ['.php'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-php');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Python',
    aliases: ['.py'],
    loader: async () => {
      try {
        return require('tree-sitter-python');
      } catch (error) {
        console.warn('tree-sitter-python not available:', error);
        return null;
      }
    },
    terminalNodeKinds: []
  },
  {
    name: 'Ruby',
    aliases: ['.rb'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-ruby');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'Rust',
    aliases: ['.rs', 'rs'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-rust');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'TypeScript',
    aliases: ['.ts', 'ts'],
    loader: async () => {
      try {
        const tsModule = require('tree-sitter-typescript');
        return tsModule.typescript;
      } catch (error) {
        console.warn('tree-sitter-typescript not available:', error);
        return null;
      }
    },
    terminalNodeKinds: []
  },
  {
    name: 'TSX',
    aliases: ['.tsx'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-typescript').then(m => m.tsx);
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'XML',
    aliases: ['.xml'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-xml');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  },
  {
    name: 'YAML',
    aliases: ['.yaml', '.yml'],
    loader: async () => {
      // In a real implementation: return await import('tree-sitter-yaml');
      return null; // Placeholder
    },
    terminalNodeKinds: []
  }
];

/**
 * Cache for loaded language configurations
 */
const languageCache = new Map<string, LanguageConfig>();

/**
 * Case-insensitive language lookup map
 */
const languageLookup = new Map<string, LazyLanguageConfig>();

// Initialize lookup map
for (const config of LANGUAGE_CONFIGS) {
  // Add primary name
  languageLookup.set(config.name.toLowerCase(), config);

  // Add aliases
  for (const alias of config.aliases) {
    languageLookup.set(alias.toLowerCase(), config);
  }
}

/**
 * Get language configuration by name or file extension
 */
export async function getLanguageConfig(language: string): Promise<LanguageConfig | null> {
  const key = language.toLowerCase();

  // Check cache first
  if (languageCache.has(key)) {
    return languageCache.get(key)!;
  }

  // Check if Tree-sitter is available
  if (!Parser) {
    console.warn('Tree-sitter not available');
    return null;
  }

  // Find language configuration
  const lazyConfig = languageLookup.get(key);
  if (!lazyConfig) {
    return null;
  }

  try {
    // Load the tree-sitter language
    const treeSitterLang = await lazyConfig.loader();
    if (!treeSitterLang) {
      console.warn(`Tree-sitter language not available for ${lazyConfig.name}`);
      return null;
    }

    // Get terminal node kind IDs
    const terminalNodeKindIds = new Set<number>();
    for (const kind of lazyConfig.terminalNodeKinds) {
      try {
        const id = treeSitterLang.idForNodeKind ? treeSitterLang.idForNodeKind(kind, true) : 0;
        if (id !== 0) {
          terminalNodeKindIds.add(id);
          console.debug(`Got id for node kind: \`${kind}\` -> ${id}`);
        } else {
          console.warn(`Failed to get id for node kind: \`${kind}\``);
        }
      } catch (error) {
        console.warn(`Error getting node kind id for ${kind}:`, error);
      }
    }

    const config: LanguageConfig = {
      name: lazyConfig.name,
      treeSitterLang,
      terminalNodeKindIds
    };

    // Cache the configuration
    languageCache.set(key, config);

    return config;
  } catch (error) {
    console.error(`Failed to load tree-sitter language for ${lazyConfig.name}:`, error);
    return null;
  }
}

/**
 * Create a Tree-sitter parser for a given language
 */
export function createParser(langConfig: LanguageConfig): any | null {
  if (!Parser) {
    console.warn('Tree-sitter not available');
    return null;
  }

  try {
    const parser = new Parser();
    parser.setLanguage(langConfig.treeSitterLang);
    return parser;
  } catch (error) {
    console.error(`Failed to create parser for ${langConfig.name}:`, error);
    return null;
  }
}

/**
 * Get list of supported languages
 */
export function getSupportedLanguages(): string[] {
  return LANGUAGE_CONFIGS.map(config => config.name);
}

/**
 * Get list of supported file extensions
 */
export function getSupportedExtensions(): string[] {
  const extensions = new Set<string>();
  for (const config of LANGUAGE_CONFIGS) {
    for (const alias of config.aliases) {
      if (alias.startsWith('.')) {
        extensions.add(alias);
      }
    }
  }
  return Array.from(extensions).sort();
}

/**
 * Detect language from file extension
 */
export function detectLanguageFromExtension(filename: string): string | null {
  const ext = filename.toLowerCase();
  const dotIndex = ext.lastIndexOf('.');
  if (dotIndex === -1) {
    return null;
  }

  const extension = ext.substring(dotIndex);
  const config = languageLookup.get(extension);
  return config ? config.name : null;
}
