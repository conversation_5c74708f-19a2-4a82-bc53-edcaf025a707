/**
 * TypeScript port of the operation framework interfaces from cocoindex
 * This file contains the interfaces for executors, factories, and argument resolvers
 */

import {
  Value,
  EnrichedValueType,
  ValueType,
  FlowInstanceContext,
  ResolvedOpArg,
  Result
} from './types';

/**
 * Operation argument schema
 */
export interface OpArgSchema {
  name: string;
  valueType: EnrichedValueType;
  analyzedValue: any; // AnalyzedValueMapping type would be defined elsewhere
}

/**
 * Simple function executor interface
 */
export interface SimpleFunctionExecutor {
  /**
   * Evaluate the operation with given arguments
   */
  evaluate(args: Value[]): Promise<Result<Value>>;

  /**
   * Whether caching is enabled for this executor
   */
  enableCache(): boolean;

  /**
   * Behavior version for cache invalidation
   * Must be Some if enableCache is true
   */
  behaviorVersion(): number | null;
}

/**
 * Simple function factory interface
 */
export interface SimpleFunctionFactory {
  /**
   * Build an executor from specification and input schema
   */
  build(
    spec: any,
    inputSchema: OpArgSchema[],
    context: FlowInstanceContext
  ): Result<{
    outputSchema: EnrichedValueType;
    executor: Promise<Result<SimpleFunctionExecutor>>;
  }>;
}

/**
 * Base interface for simple function factories
 */
export interface SimpleFunctionFactoryBase<TSpec = any, TResolvedArgs = any>
  extends SimpleFunctionFactory {

  /**
   * Get the name of this operation
   */
  name(): string;

  /**
   * Resolve schema and arguments
   */
  resolveSchema(
    spec: TSpec,
    argsResolver: OpArgsResolver,
    context: FlowInstanceContext
  ): Result<{
    resolvedArgs: TResolvedArgs;
    outputSchema: EnrichedValueType;
  }>;

  /**
   * Build executor with resolved arguments
   */
  buildExecutor(
    spec: TSpec,
    resolvedArgs: TResolvedArgs,
    context: FlowInstanceContext
  ): Promise<Result<SimpleFunctionExecutor>>;
}

/**
 * Operation arguments resolver
 */
export class OpArgsResolver {
  private args: OpArgSchema[];
  private nextPositionalIdx: number;
  private remainingKwargs: Map<string, number>;

  constructor(args: OpArgSchema[]) {
    this.args = args;
    this.nextPositionalIdx = 0;
    this.remainingKwargs = new Map();

    // Initialize kwargs map
    args.forEach((arg, idx) => {
      this.remainingKwargs.set(arg.name, idx);
    });
  }

  /**
   * Get the next positional argument
   */
  nextArg(name: string): Result<ResolvedOpArg> {
    if (this.nextPositionalIdx >= this.args.length) {
      return { success: false, error: new Error(`No more positional arguments available for ${name}`) };
    }

    const arg = this.args[this.nextPositionalIdx];
    this.nextPositionalIdx++;
    this.remainingKwargs.delete(arg.name);

    return {
      success: true,
      data: {
        name: arg.name,
        typ: arg.valueType,
        idx: this.nextPositionalIdx - 1
      }
    };
  }

  /**
   * Get the next optional argument
   */
  nextOptionalArg(_name: string): Result<ResolvedOpArg | null> {
    if (this.nextPositionalIdx >= this.args.length) {
      return { success: true, data: null };
    }

    const arg = this.args[this.nextPositionalIdx];
    this.nextPositionalIdx++;
    this.remainingKwargs.delete(arg.name);

    return {
      success: true,
      data: {
        name: arg.name,
        typ: arg.valueType,
        idx: this.nextPositionalIdx - 1
      }
    };
  }

  /**
   * Get analyzed value for an argument
   */
  getAnalyzeValue(arg: ResolvedOpArg): any {
    return this.args[arg.idx]?.analyzedValue;
  }

  /**
   * Ensure all arguments have been consumed
   */
  done(): Result<void> {
    if (this.remainingKwargs.size > 0) {
      const remaining = Array.from(this.remainingKwargs.keys()).join(', ');
      return {
        success: false,
        error: new Error(`Unused arguments: ${remaining}`)
      };
    }
    return { success: true, data: undefined };
  }
}

/**
 * Extensions for resolved operation arguments
 */
export class ResolvedOpArgExt {
  /**
   * Expect a specific type for the argument
   */
  static expectType(arg: ResolvedOpArg, _expectedType: ValueType): Result<ResolvedOpArg> {
    // Type checking logic would go here
    // For now, just return the argument as-is
    return { success: true, data: arg };
  }

  /**
   * Get value from arguments array
   */
  static getValue(arg: ResolvedOpArg, args: Value[]): Result<Value> {
    if (arg.idx >= args.length) {
      return {
        success: false,
        error: new Error(`Argument index ${arg.idx} out of bounds`)
      };
    }
    return { success: true, data: args[arg.idx] };
  }

  /**
   * Take value from arguments array (consuming it)
   */
  static takeValue(arg: ResolvedOpArg, args: Value[]): Result<Value> {
    if (arg.idx >= args.length) {
      return {
        success: false,
        error: new Error(`Argument index ${arg.idx} out of bounds`)
      };
    }
    const value = args[arg.idx];
    args[arg.idx] = { type: 'null' }; // Mark as consumed
    return { success: true, data: value };
  }
}

/**
 * Extensions for optional resolved operation arguments
 */
export class OptionalResolvedOpArgExt {
  /**
   * Expect a specific type for the optional argument
   */
  static expectType(
    arg: ResolvedOpArg | null,
    expectedType: ValueType
  ): Result<ResolvedOpArg | null> {
    if (arg === null) {
      return { success: true, data: null };
    }
    return ResolvedOpArgExt.expectType(arg, expectedType);
  }

  /**
   * Get value from arguments array, returning null value if argument is null
   */
  static getValue(arg: ResolvedOpArg | null, args: Value[]): Result<Value> {
    if (arg === null) {
      return { success: true, data: { type: 'null' } };
    }
    return ResolvedOpArgExt.getValue(arg, args);
  }

  /**
   * Take value from arguments array, returning null value if argument is null
   */
  static takeValue(arg: ResolvedOpArg | null, args: Value[]): Result<Value> {
    if (arg === null) {
      return { success: true, data: { type: 'null' } };
    }
    return ResolvedOpArgExt.takeValue(arg, args);
  }
}

/**
 * Value helper functions
 */
export class ValueHelpers {
  /**
   * Get string value from a Value
   */
  static asStr(value: Value): Result<string> {
    if (value.type === 'basic' && value.value.type === 'str') {
      return { success: true, data: value.value.value };
    }
    return {
      success: false,
      error: new Error(`Expected string value, got ${value.type}`)
    };
  }

  /**
   * Get integer value from a Value
   */
  static asInt64(value: Value): Result<number> {
    if (value.type === 'basic' && value.value.type === 'int64') {
      return { success: true, data: value.value.value };
    }
    return {
      success: false,
      error: new Error(`Expected int64 value, got ${value.type}`)
    };
  }

  /**
   * Get optional value (null returns null, otherwise returns the value)
   */
  static optional(value: Value): Value | null {
    if (value.type === 'null') {
      return null;
    }
    return value;
  }
}

/**
 * Field attributes constants
 */
export const FIELD_ATTRS = {
  COCOINDEX_PREFIX: 'cocoindex.io/',
  CONTENT_FILENAME: 'cocoindex.io/content_filename',
  CONTENT_MIME_TYPE: 'cocoindex.io/content_mime_type',
  CHUNK_BASE_TEXT: 'cocoindex.io/chunk_base_text',
  VECTOR_ORIGIN_TEXT: 'cocoindex.io/vector_origin_text',
} as const;
