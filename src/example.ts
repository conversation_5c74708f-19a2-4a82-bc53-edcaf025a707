/**
 * Example usage of the TypeScript recursive text splitter
 */

import { RecursiveChunker } from './recursive-chunker';
import { getLanguageConfig, detectLanguageFromExtension } from './tree-sitter-config';

/**
 * Basic text splitting example
 */
async function basicTextSplitting() {
  console.log('=== Basic Text Splitting ===');
  
  const text = `
This is a long document that needs to be split into smaller chunks for processing.
It contains multiple paragraphs and sentences that should be intelligently divided.

The recursive text splitter will first try to split on double newlines, then single newlines,
and finally on whitespace if needed to meet the chunk size requirements.

This approach ensures that the semantic structure of the text is preserved as much as possible
while still meeting the size constraints for downstream processing.
  `.trim();

  const chunker = new RecursiveChunker(text, null, 150, 20);
  
  const result = chunker.splitRootChunk({
    type: 'regexpSepChunk',
    nextRegexpSepId: 0
  });

  if (result.success) {
    const chunks = result.data;
    console.log(`Split into ${chunks.length} chunks:`);
    
    chunks.forEach((chunk, index) => {
      console.log(`\nChunk ${index + 1} (${chunk[0].start}-${chunk[0].end}):`);
      console.log(`"${chunk[1]}"`);
    });
  } else {
    console.error('Failed to split text:', result.error);
  }
}

/**
 * Code splitting example with language detection
 */
async function codeSplittingExample() {
  console.log('\n=== Code Splitting Example ===');
  
  const jsCode = `
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

function factorial(n) {
  if (n <= 1) return 1;
  return n * factorial(n - 1);
}

class Calculator {
  constructor() {
    this.history = [];
  }
  
  add(a, b) {
    const result = a + b;
    this.history.push({ operation: 'add', a, b, result });
    return result;
  }
  
  multiply(a, b) {
    const result = a * b;
    this.history.push({ operation: 'multiply', a, b, result });
    return result;
  }
}

export { fibonacci, factorial, Calculator };
  `.trim();

  // Detect language from filename
  const language = detectLanguageFromExtension('example.js');
  console.log(`Detected language: ${language}`);

  // Get language configuration
  const langConfig = await getLanguageConfig('javascript');
  
  if (langConfig) {
    console.log(`Using Tree-sitter parser for ${langConfig.name}`);
  } else {
    console.log('Tree-sitter parser not available, using regex splitting');
  }

  const chunker = new RecursiveChunker(jsCode, langConfig, 200, 30);
  
  const result = chunker.splitRootChunk({
    type: 'regexpSepChunk', // Would be 'treeSitterNode' with actual Tree-sitter integration
    nextRegexpSepId: 0
  });

  if (result.success) {
    const chunks = result.data;
    console.log(`Split code into ${chunks.length} chunks:`);
    
    chunks.forEach((chunk, index) => {
      console.log(`\nCode Chunk ${index + 1} (${chunk[0].start}-${chunk[0].end}):`);
      console.log('```javascript');
      console.log(chunk[1]);
      console.log('```');
    });
  } else {
    console.error('Failed to split code:', result.error);
  }
}

/**
 * Unicode text handling example
 */
async function unicodeExample() {
  console.log('\n=== Unicode Text Example ===');
  
  const unicodeText = `
Hello 世界! This text contains various Unicode characters: 🌍🚀✨

Mathematical symbols: ∑∏∫∆∇∂ℝℂℕℤ

Emoji sequences: 👨‍💻👩‍🔬🧑‍🎨

Different scripts:
- Arabic: مرحبا بالعالم
- Chinese: 你好世界
- Japanese: こんにちは世界
- Korean: 안녕하세요 세계
- Russian: Привет мир
- Hindi: नमस्ते दुनिया

This demonstrates proper handling of multi-byte UTF-8 characters.
  `.trim();

  const chunker = new RecursiveChunker(unicodeText, null, 100, 15);
  
  const result = chunker.splitRootChunk({
    type: 'regexpSepChunk',
    nextRegexpSepId: 0
  });

  if (result.success) {
    const chunks = result.data;
    console.log(`Split Unicode text into ${chunks.length} chunks:`);
    
    chunks.forEach((chunk, index) => {
      console.log(`\nUnicode Chunk ${index + 1} (${chunk[0].start}-${chunk[0].end}):`);
      console.log(`"${chunk[1]}"`);
      console.log(`Character count: ${chunk[1].length}`);
      console.log(`Byte count: ${new TextEncoder().encode(chunk[1]).length}`);
    });
  } else {
    console.error('Failed to split Unicode text:', result.error);
  }
}

/**
 * Overlap demonstration
 */
async function overlapExample() {
  console.log('\n=== Overlap Demonstration ===');
  
  const text = "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet. It's commonly used for testing fonts and keyboards.";

  console.log('Original text:');
  console.log(`"${text}"`);
  console.log(`Length: ${text.length} characters\n`);

  // Split with different overlap settings
  const overlapSettings = [0, 10, 20];
  
  for (const overlap of overlapSettings) {
    console.log(`--- Overlap: ${overlap} characters ---`);
    
    const chunker = new RecursiveChunker(text, null, 50, overlap);
    const result = chunker.splitRootChunk({
      type: 'regexpSepChunk',
      nextRegexpSepId: 0
    });

    if (result.success) {
      const chunks = result.data;
      console.log(`Chunks: ${chunks.length}`);
      
      chunks.forEach((chunk, index) => {
        console.log(`  ${index + 1}: "${chunk[1]}" (${chunk[1].length} chars)`);
      });
      
      // Show overlap between consecutive chunks
      for (let i = 0; i < chunks.length - 1; i++) {
        const current = chunks[i][1];
        const next = chunks[i + 1][1];
        
        // Find common suffix/prefix
        let overlapLength = 0;
        const minLength = Math.min(current.length, next.length);
        
        for (let j = 1; j <= minLength; j++) {
          if (current.slice(-j) === next.slice(0, j)) {
            overlapLength = j;
          }
        }
        
        if (overlapLength > 0) {
          console.log(`    Overlap between chunks ${i + 1} and ${i + 2}: "${current.slice(-overlapLength)}" (${overlapLength} chars)`);
        }
      }
    }
    console.log();
  }
}

/**
 * Performance benchmark
 */
async function performanceBenchmark() {
  console.log('\n=== Performance Benchmark ===');
  
  // Generate a large text for testing
  const baseText = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. ".repeat(100);
  const largeText = baseText.repeat(10); // ~56,000 characters
  
  console.log(`Testing with ${largeText.length} characters`);
  
  const chunkSizes = [500, 1000, 2000];
  
  for (const chunkSize of chunkSizes) {
    const startTime = performance.now();
    
    const chunker = new RecursiveChunker(largeText, null, chunkSize, 100);
    const result = chunker.splitRootChunk({
      type: 'regexpSepChunk',
      nextRegexpSepId: 0
    });
    
    const endTime = performance.now();
    
    if (result.success) {
      const chunks = result.data;
      console.log(`Chunk size ${chunkSize}: ${chunks.length} chunks in ${(endTime - startTime).toFixed(2)}ms`);
    }
  }
}

/**
 * Run all examples
 */
async function runExamples() {
  console.log('TypeScript Recursive Text Splitter Examples\n');
  
  try {
    await basicTextSplitting();
    await codeSplittingExample();
    await unicodeExample();
    await overlapExample();
    await performanceBenchmark();
    
    console.log('\n✅ All examples completed successfully!');
  } catch (error) {
    console.error('\n❌ Example failed:', error);
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  runExamples();
}

export {
  basicTextSplitting,
  codeSplittingExample,
  unicodeExample,
  overlapExample,
  performanceBenchmark,
  runExamples
};
