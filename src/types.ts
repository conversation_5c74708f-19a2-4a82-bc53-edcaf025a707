/**
 * TypeScript port of the Rust value system from cocoindex
 * This file contains the core type definitions for values, ranges, and schemas
 */

/**
 * Represents a range with start and end positions
 */
export class RangeValue {
  public start: number;
  public end: number;

  constructor(start: number, end: number) {
    this.start = start;
    this.end = end;
  }

  /**
   * Get the length of the range
   */
  get length(): number {
    return this.end - this.start;
  }

  /**
   * Extract substring from text using this range
   */
  extractStr(text: string): string {
    return text.slice(this.start, this.end);
  }

  /**
   * Convert to tuple format for serialization
   */
  toTuple(): [number, number] {
    return [this.start, this.end];
  }

  /**
   * Create from tuple format
   */
  static fromTuple([start, end]: [number, number]): RangeValue {
    return new RangeValue(start, end);
  }
}

/**
 * Basic value types that can be stored
 */
export enum BasicValueType {
  Bytes = 'bytes',
  Str = 'str',
  Bool = 'bool',
  Int64 = 'int64',
  Float32 = 'float32',
  Float64 = 'float64',
  Range = 'range',
  Uuid = 'uuid',
  Date = 'date',
  Time = 'time',
  LocalDateTime = 'localDateTime',
  OffsetDateTime = 'offsetDateTime',
  TimeDelta = 'timeDelta',
  Json = 'json',
  Vector = 'vector',
}

/**
 * Basic value that can be stored in the system
 */
export type BasicValue =
  | { type: BasicValueType.Bytes; value: Uint8Array }
  | { type: BasicValueType.Str; value: string }
  | { type: BasicValueType.Bool; value: boolean }
  | { type: BasicValueType.Int64; value: number }
  | { type: BasicValueType.Float32; value: number }
  | { type: BasicValueType.Float64; value: number }
  | { type: BasicValueType.Range; value: RangeValue }
  | { type: BasicValueType.Uuid; value: string }
  | { type: BasicValueType.Date; value: Date }
  | { type: BasicValueType.Time; value: Date }
  | { type: BasicValueType.LocalDateTime; value: Date }
  | { type: BasicValueType.OffsetDateTime; value: Date }
  | { type: BasicValueType.TimeDelta; value: number }
  | { type: BasicValueType.Json; value: any }
  | { type: BasicValueType.Vector; value: BasicValue[] };

/**
 * Key value for indexing
 */
export type KeyValue =
  | { type: 'bytes'; value: Uint8Array }
  | { type: 'str'; value: string }
  | { type: 'bool'; value: boolean }
  | { type: 'int64'; value: number }
  | { type: 'range'; value: RangeValue }
  | { type: 'uuid'; value: string }
  | { type: 'date'; value: Date }
  | { type: 'struct'; value: KeyValue[] };

/**
 * Field values container
 */
export interface FieldValues<VS = ScopeValue> {
  fields: Value<VS>[];
}

/**
 * Scope value type
 */
export interface ScopeValue {
  type: string;
  value?: any;
}

/**
 * Main value type that can represent any data
 */
export type Value<VS = ScopeValue> =
  | { type: 'null' }
  | { type: 'basic'; value: BasicValue }
  | { type: 'struct'; value: FieldValues<VS> }
  | { type: 'utable'; value: VS[] }
  | { type: 'ktable'; value: Map<KeyValue, VS> }
  | { type: 'ltable'; value: VS[] };

/**
 * Value type schema definition
 */
export type ValueType =
  | { type: 'basic'; basicType: BasicValueType }
  | { type: 'struct'; schema: StructSchema }
  | { type: 'table'; schema: TableSchema };

/**
 * Enriched value type with attributes
 */
export interface EnrichedValueType<DataType = ValueType> {
  typ: DataType;
  attrs: Record<string, any>;
  nullable: boolean;
}

/**
 * Field schema definition
 */
export interface FieldSchema<DataType = ValueType> {
  name: string;
  valueType: EnrichedValueType<DataType>;
}

/**
 * Struct schema definition
 */
export interface StructSchema {
  fields: FieldSchema[];
}

/**
 * Table schema definition
 */
export interface TableSchema {
  kind: 'KTable' | 'UTable' | 'LTable';
  schema: StructSchema;
}

/**
 * Resolved operation argument
 */
export interface ResolvedOpArg {
  name: string;
  typ: EnrichedValueType;
  idx: number;
}

/**
 * Empty specification type
 */
export interface EmptySpec { }

/**
 * Flow instance context
 */
export interface FlowInstanceContext {
  flowInstanceName: string;
  authRegistry: any; // AuthRegistry type would be defined elsewhere
  pyExecCtx?: any; // Python execution context
}

/**
 * Result type for operations
 */
export type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };

/**
 * Helper function to create successful result
 */
export function Ok<T>(data: T): Result<T> {
  return { success: true, data };
}

/**
 * Helper function to create error result
 */
export function Err<E = Error>(error: E): Result<never, E> {
  return { success: false, error };
}

/**
 * Helper function to create output type
 */
export function makeOutputType<Type extends ValueType>(valueType: Type): EnrichedValueType {
  return {
    typ: valueType,
    attrs: {},
    nullable: false,
  };
}

/**
 * Create a basic value
 */
export function createBasicValue(type: BasicValueType, value: any): BasicValue {
  return { type, value } as BasicValue;
}

/**
 * Create a string value
 */
export function createStrValue(value: string): Value {
  return {
    type: 'basic',
    value: { type: BasicValueType.Str, value }
  };
}

/**
 * Create a range value
 */
export function createRangeValue(start: number, end: number): Value {
  return {
    type: 'basic',
    value: { type: BasicValueType.Range, value: new RangeValue(start, end) }
  };
}

/**
 * Create a KTable value
 */
export function createKTableValue(entries: Array<[KeyValue, Value]>): Value {
  const map = new Map<KeyValue, Value>();
  entries.forEach(([key, value]) => map.set(key, value));
  return {
    type: 'ktable',
    value: map
  };
}

/**
 * Helper to create field values
 */
export function fieldsValue(...fields: Value[]): FieldValues {
  return { fields };
}
