/**
 * TypeScript port of the recursive text chunker from cocoindex
 * This file contains the core text splitting logic
 */

import { RangeValue, Result, Ok } from './types';

/**
 * Text separators for different levels of splitting
 */
const TEXT_SEPARATORS = [
  /\n\n+/g,  // Double newlines and more
  /\n/g,     // Single newlines
  /\s+/g     // Whitespace
];

/**
 * Language configuration for Tree-sitter parsing
 */
export interface LanguageConfig {
  name: string;
  treeSitterLang: any; // Tree-sitter language object
  terminalNodeKindIds: Set<number>;
}

/**
 * Chunk kind enumeration
 */
export type ChunkKind =
  | { type: 'treeSitterNode'; node: any }
  | { type: 'regexpSepChunk'; nextRegexpSepId: number }
  | { type: 'leafText' };

/**
 * Chunk representation
 */
export interface Chunk {
  fullText: string;
  range: RangeValue;
  kind: ChunkKind;
}

/**
 * Get text content of a chunk
 */
function getChunkText(chunk: Chunk): string {
  return chunk.range.extractStr(chunk.fullText);
}

/**
 * Text chunks iterator for regexp-based splitting
 */
export class TextChunksIterator implements Iterator<Chunk> {
  private parent: Chunk;
  private regexpSepId: number;
  private regex: RegExp;
  private lastIndex: number;
  private nextStartPos: number | null;
  private done: boolean;

  constructor(parent: Chunk, regexpSepId: number) {
    this.parent = parent;
    this.regexpSepId = regexpSepId;
    this.regex = new RegExp(TEXT_SEPARATORS[regexpSepId]);
    this.lastIndex = 0;
    this.nextStartPos = parent.range.start;
    this.done = false;
  }

  next(): IteratorResult<Chunk> {
    if (this.done || this.nextStartPos === null) {
      return { done: true, value: undefined };
    }

    const startPos = this.nextStartPos;
    const parentText = getChunkText(this.parent);

    // Find next match
    this.regex.lastIndex = this.lastIndex;
    const match = this.regex.exec(parentText);

    let endPos: number;
    if (match) {
      endPos = this.parent.range.start + match.index;
      this.nextStartPos = this.parent.range.start + match.index + match[0].length;
      this.lastIndex = match.index + match[0].length;
    } else {
      endPos = this.parent.range.end;
      this.nextStartPos = null;
      this.done = true;
    }

    // Skip empty chunks
    if (startPos >= endPos) {
      return this.next();
    }

    const chunk: Chunk = {
      fullText: this.parent.fullText,
      range: new RangeValue(startPos, endPos),
      kind: {
        type: 'regexpSepChunk',
        nextRegexpSepId: this.regexpSepId + 1
      }
    };

    return { done: false, value: chunk };
  }

  [Symbol.iterator](): Iterator<Chunk> {
    return this;
  }
}

/**
 * Tree-sitter node iterator for syntax-aware splitting
 */
export class TreeSitterNodeIterator implements Iterator<Chunk> {
  private fullText: string;
  private cursor: any | null; // Tree-sitter cursor
  private nextStartPos: number;
  private endPos: number;
  private done: boolean;

  constructor(fullText: string, cursor: any, startPos: number, endPos: number) {
    this.fullText = fullText;
    this.cursor = cursor;
    this.nextStartPos = startPos;
    this.endPos = endPos;
    this.done = false;
  }

  private fillGap(gapEndPos: number): Chunk | null {
    const startPos = this.nextStartPos;
    if (startPos < gapEndPos) {
      this.nextStartPos = gapEndPos;
      return {
        fullText: this.fullText,
        range: new RangeValue(startPos, gapEndPos),
        kind: { type: 'leafText' }
      };
    }
    return null;
  }

  next(): IteratorResult<Chunk> {
    if (this.done) {
      return { done: true, value: undefined };
    }

    if (!this.cursor) {
      const gap = this.fillGap(this.endPos);
      this.done = true;
      if (gap) {
        return { done: false, value: gap };
      }
      return { done: true, value: undefined };
    }

    // In Tree-sitter, the cursor itself represents the current node
    const node = this.cursor.currentNode || this.cursor;
    if (!node) {
      this.cursor = null;
      return this.next();
    }

    // Tree-sitter uses startIndex/endIndex for byte positions
    const nodeStartByte = node.startIndex !== undefined ? node.startIndex : 0;
    const nodeEndByte = node.endIndex !== undefined ? node.endIndex : this.fullText.length;

    const gap = this.fillGap(nodeStartByte);
    if (gap) {
      return { done: false, value: gap };
    }

    // Move to next sibling
    if (!this.cursor.gotoNextSibling()) {
      this.cursor = null;
    }

    this.nextStartPos = nodeEndByte;
    const chunk: Chunk = {
      fullText: this.fullText,
      range: new RangeValue(nodeStartByte, nodeEndByte),
      kind: { type: 'treeSitterNode', node }
    };

    return { done: false, value: chunk };
  }

  [Symbol.iterator](): Iterator<Chunk> {
    return this;
  }
}

/**
 * Translate byte positions to character positions for Unicode text
 * This simulates the Rust behavior where byte positions need to be converted to Unicode scalar value positions
 */
export function translateBytesToChars(text: string, offsets: number[]): void {
  // Build a mapping from byte positions to character positions
  const byteToCharMap: number[] = [];
  const encoder = new TextEncoder();

  let bytePos = 0;
  let charPos = 0;

  // Process each Unicode code point (not UTF-16 code unit)
  for (let i = 0; i < text.length;) {
    const codePoint = text.codePointAt(i);
    if (codePoint === undefined) break;

    // Record the character position for this byte position
    byteToCharMap[bytePos] = charPos;

    // Get UTF-8 byte length for this code point
    const char = String.fromCodePoint(codePoint);
    const charBytes = encoder.encode(char);
    const byteLength = charBytes.length;

    // Fill in intermediate byte positions with the same character position
    for (let j = 1; j < byteLength; j++) {
      byteToCharMap[bytePos + j] = charPos;
    }

    bytePos += byteLength;
    charPos++;

    // Move to next code point (may be 1 or 2 UTF-16 code units)
    i += codePoint > 0xFFFF ? 2 : 1;
  }

  // Add final position
  byteToCharMap[bytePos] = charPos;

  // Convert offsets
  for (let i = 0; i < offsets.length; i++) {
    const originalBytePos = offsets[i];

    if (originalBytePos < byteToCharMap.length) {
      offsets[i] = byteToCharMap[originalBytePos];
    } else {
      // If beyond the end, use the final character position
      offsets[i] = charPos;
    }
  }
}

/**
 * Recursive text chunker
 */
export class RecursiveChunker {
  private fullText: string;
  private langConfig: LanguageConfig | null;
  private chunkSize: number;
  private chunkOverlap: number;

  constructor(
    fullText: string,
    langConfig: LanguageConfig | null,
    chunkSize: number,
    chunkOverlap: number
  ) {
    this.fullText = fullText;
    this.langConfig = langConfig;
    this.chunkSize = chunkSize;
    this.chunkOverlap = chunkOverlap;
  }

  /**
   * Flush accumulated small chunks
   */
  private flushSmallChunks(
    chunks: RangeValue[],
    output: Array<[RangeValue, string]>
  ): void {
    if (chunks.length === 0) return;

    let startPos = chunks[0].start;
    for (let i = 0; i < chunks.length - 1; i++) {
      const nextChunk = chunks[i + 1];
      if (nextChunk.end - startPos > this.chunkSize) {
        const chunk = chunks[i];
        this.addOutput(new RangeValue(startPos, chunk.end), output);

        // Find new start position with overlap
        let newStartIdx = i + 1;
        while (newStartIdx > 0) {
          const prevPos = chunks[newStartIdx - 1].start;
          if (prevPos <= startPos ||
            chunk.end - prevPos > this.chunkOverlap ||
            nextChunk.end - prevPos > this.chunkSize) {
            break;
          }
          newStartIdx--;
        }
        startPos = chunks[newStartIdx].start;
      }
    }

    const lastChunk = chunks[chunks.length - 1];
    this.addOutput(new RangeValue(startPos, lastChunk.end), output);
  }

  /**
   * Process sub-chunks from an iterator
   */
  private processSubChunks(
    subChunksIter: Iterator<Chunk>,
    output: Array<[RangeValue, string]>
  ): Result<void> {
    const smallChunks: RangeValue[] = [];

    for (const subChunk of { [Symbol.iterator]: () => subChunksIter }) {
      const subRange = subChunk.range;
      if (subRange.length <= this.chunkSize) {
        smallChunks.push(subRange);
      } else {
        this.flushSmallChunks(smallChunks, output);
        smallChunks.length = 0;
        const result = this.splitSubstring(subChunk, output);
        if (!result.success) {
          return result;
        }
      }
    }

    this.flushSmallChunks(smallChunks, output);
    return Ok(undefined);
  }

  /**
   * Split a substring recursively
   */
  private splitSubstring(
    chunk: Chunk,
    output: Array<[RangeValue, string]>
  ): Result<void> {
    switch (chunk.kind.type) {
      case 'treeSitterNode': {
        const node = chunk.kind.node;
        if (this.langConfig && node) {
          // Check if this is a terminal node that shouldn't be split further
          const nodeKindId = node.kindId || node.type || 0;
          const isTerminalNode = this.langConfig.terminalNodeKindIds.has(nodeKindId);

          if (!isTerminalNode) {
            // Try to split into child nodes
            const childCount = node.childCount || node.children?.length || 0;
            if (childCount > 0) {
              try {
                const cursor = node.walk ? node.walk() : null;
                if (cursor && cursor.gotoFirstChild()) {
                  const nodeStartByte = node.startIndex || node.startPosition?.row * this.fullText.length || 0;
                  const nodeEndByte = node.endIndex || node.endPosition?.row * this.fullText.length || this.fullText.length;

                  const iterator = new TreeSitterNodeIterator(
                    this.fullText,
                    cursor,
                    nodeStartByte,
                    nodeEndByte
                  );
                  return this.processSubChunks(iterator, output);
                }
              } catch (error) {
                console.warn('Error processing Tree-sitter node:', error);
              }
            }
          }
        }
        this.addOutput(chunk.range, output);
        break;
      }

      case 'regexpSepChunk': {
        if (chunk.kind.nextRegexpSepId >= TEXT_SEPARATORS.length) {
          this.addOutput(chunk.range, output);
        } else {
          const iterator = new TextChunksIterator(chunk, chunk.kind.nextRegexpSepId);
          return this.processSubChunks(iterator, output);
        }
        break;
      }

      case 'leafText': {
        this.addOutput(chunk.range, output);
        break;
      }
    }

    return Ok(undefined);
  }

  /**
   * Split the root chunk
   */
  splitRootChunk(kind: ChunkKind): Result<Array<[RangeValue, string]>> {
    const output: Array<[RangeValue, string]> = [];
    const rootChunk: Chunk = {
      fullText: this.fullText,
      range: new RangeValue(0, this.fullText.length),
      kind
    };

    const result = this.splitSubstring(rootChunk, output);
    if (!result.success) {
      return result;
    }

    return Ok(output);
  }

  /**
   * Add output chunk with trimming and filtering
   */
  private addOutput(
    range: RangeValue,
    output: Array<[RangeValue, string]>
  ): void {
    const text = range.extractStr(this.fullText);

    // Trim leading newlines
    const trimmedText = text.replace(/^[\n\r]+/, '');
    const adjustedStart = range.start + (text.length - trimmedText.length);

    // Trim trailing whitespace
    const finalText = trimmedText.replace(/\s+$/, '');

    // Only record chunks with alphanumeric characters
    if (/[a-zA-Z0-9]/.test(finalText)) {
      output.push([
        new RangeValue(adjustedStart, adjustedStart + finalText.length),
        finalText
      ]);
    }
  }
}
