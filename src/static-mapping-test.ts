/**
 * Test the static language mapping table (TREE_SITTER_LANGUAGE_BY_LANG equivalent)
 */

import { 
  getLanguageConfigSync, 
  getSupportedLanguages, 
  getSupportedExtensions,
  detectLanguageFromExtension,
  isLanguageSupported,
  getLanguageAliases
} from './tree-sitter-config';

/**
 * Test static language mapping functionality
 */
function testStaticLanguageMapping() {
  console.log('=== Static Language Mapping Test ===');
  
  // Test getting supported languages
  const languages = getSupportedLanguages();
  console.log(`✓ Found ${languages.length} supported languages:`);
  console.log(`  ${languages.slice(0, 10).join(', ')}${languages.length > 10 ? '...' : ''}`);
  
  // Test getting supported extensions
  const extensions = getSupportedExtensions();
  console.log(`✓ Found ${extensions.length} supported extensions:`);
  console.log(`  ${extensions.slice(0, 10).join(', ')}${extensions.length > 10 ? '...' : ''}`);
  
  // Test language detection from extension
  const testFiles = ['test.js', 'main.py', 'app.rs', 'style.css', 'data.json'];
  console.log('✓ Language detection from file extensions:');
  for (const file of testFiles) {
    const lang = detectLanguageFromExtension(file);
    console.log(`  ${file} -> ${lang || 'unknown'}`);
  }
  
  // Test language support checking
  const testLangs = ['JavaScript', 'Python', 'Rust', 'UnknownLang'];
  console.log('✓ Language support checking:');
  for (const lang of testLangs) {
    const supported = isLanguageSupported(lang);
    console.log(`  ${lang}: ${supported ? 'supported' : 'not supported'}`);
  }
  
  // Test getting language aliases
  console.log('✓ Language aliases:');
  const aliasTests = ['JavaScript', 'TypeScript', 'C++'];
  for (const lang of aliasTests) {
    const aliases = getLanguageAliases(lang);
    console.log(`  ${lang}: [${aliases.join(', ')}]`);
  }
  
  return true;
}

/**
 * Test direct language configuration access
 */
function testDirectLanguageAccess() {
  console.log('\n=== Direct Language Access Test ===');
  
  // Test getting JavaScript configuration
  const jsConfig = getLanguageConfigSync('JavaScript');
  if (jsConfig) {
    console.log(`✓ JavaScript config loaded: ${jsConfig.name}`);
    console.log(`  Tree-sitter language: ${jsConfig.treeSitterLang ? 'loaded' : 'not loaded'}`);
    console.log(`  Terminal node kinds: ${jsConfig.terminalNodeKindIds.size}`);
  } else {
    console.log('❌ Failed to load JavaScript config');
    return false;
  }
  
  // Test case-insensitive access
  const jsConfigLower = getLanguageConfigSync('javascript');
  if (jsConfigLower && jsConfigLower === jsConfig) {
    console.log('✓ Case-insensitive access works');
  } else {
    console.log('❌ Case-insensitive access failed');
    return false;
  }
  
  // Test alias access
  const jsConfigAlias = getLanguageConfigSync('.js');
  if (jsConfigAlias && jsConfigAlias === jsConfig) {
    console.log('✓ Alias access works');
  } else {
    console.log('❌ Alias access failed');
    return false;
  }
  
  // Test non-existent language
  const unknownConfig = getLanguageConfigSync('UnknownLanguage');
  if (unknownConfig === null) {
    console.log('✓ Non-existent language returns null');
  } else {
    console.log('❌ Non-existent language should return null');
    return false;
  }
  
  return true;
}

/**
 * Test lazy loading behavior
 */
function testLazyLoading() {
  console.log('\n=== Lazy Loading Test ===');
  
  // Test that languages are loaded on first access
  const pythonConfig1 = getLanguageConfigSync('Python');
  const pythonConfig2 = getLanguageConfigSync('Python');
  
  if (pythonConfig1 === pythonConfig2) {
    console.log('✓ Same config object returned on multiple accesses (cached)');
  } else {
    console.log('❌ Different config objects returned (not cached properly)');
    return false;
  }
  
  // Test multiple language access
  const languages = ['JavaScript', 'Python', 'TypeScript', 'Rust'];
  const configs = languages.map(lang => getLanguageConfigSync(lang));
  
  const loadedCount = configs.filter(config => config !== null).length;
  console.log(`✓ Loaded ${loadedCount}/${languages.length} language configurations`);
  
  // Test that each language has a unique config object
  const uniqueConfigs = new Set(configs.filter(c => c !== null));
  if (uniqueConfigs.size === loadedCount) {
    console.log('✓ Each language has a unique configuration object');
  } else {
    console.log('❌ Some languages share configuration objects incorrectly');
    return false;
  }
  
  return true;
}

/**
 * Test Rust compatibility
 */
function testRustCompatibility() {
  console.log('\n=== Rust Compatibility Test ===');
  
  // Test that the same languages are supported as in Rust
  const expectedLanguages = [
    'C', 'C++', 'C#', 'CSS', 'Fortran', 'Go', 'HTML', 'Java',
    'JavaScript', 'JSON', 'Markdown', 'Pascal', 'PHP', 'Python',
    'R', 'Ruby', 'Rust', 'Scala', 'SQL', 'Swift', 'TOML',
    'TSX', 'TypeScript', 'XML', 'DTD', 'YAML'
  ];
  
  const supportedLanguages = getSupportedLanguages();
  const missingLanguages = expectedLanguages.filter(lang => !supportedLanguages.includes(lang));
  const extraLanguages = supportedLanguages.filter(lang => !expectedLanguages.includes(lang));
  
  if (missingLanguages.length === 0) {
    console.log('✓ All expected languages are supported');
  } else {
    console.log(`⚠️  Missing languages: ${missingLanguages.join(', ')}`);
  }
  
  if (extraLanguages.length === 0) {
    console.log('✓ No unexpected extra languages');
  } else {
    console.log(`ℹ️  Extra languages: ${extraLanguages.join(', ')}`);
  }
  
  // Test specific language aliases from Rust
  const rustAliases = getLanguageAliases('Rust');
  const expectedRustAliases = ['.rs', 'rs'];
  const hasAllRustAliases = expectedRustAliases.every(alias => rustAliases.includes(alias));
  
  if (hasAllRustAliases) {
    console.log('✓ Rust language aliases match expected');
  } else {
    console.log(`❌ Rust aliases mismatch. Expected: [${expectedRustAliases.join(', ')}], Got: [${rustAliases.join(', ')}]`);
    return false;
  }
  
  return true;
}

/**
 * Run all static mapping tests
 */
function runStaticMappingTests() {
  console.log('Static Language Mapping Tests\n');
  
  const tests = [
    testStaticLanguageMapping,
    testDirectLanguageAccess,
    testLazyLoading,
    testRustCompatibility
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    try {
      const result = test();
      if (result) {
        passed++;
      }
    } catch (error) {
      console.log('❌ Test failed with error:', error);
    }
  }
  
  console.log(`\n📊 Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('✅ All static mapping tests passed!');
  } else {
    console.log('⚠️  Some static mapping tests failed');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runStaticMappingTests();
}

export {
  testStaticLanguageMapping,
  testDirectLanguageAccess,
  testLazyLoading,
  testRustCompatibility,
  runStaticMappingTests
};
