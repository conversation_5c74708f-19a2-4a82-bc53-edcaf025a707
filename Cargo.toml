[package]
name = "cocoindex"
# Version used for local development is always higher than others to take precedence.
# Will be overridden for specific release versions.
version = "999.0.0"
edition = "2024"

[profile.release]
codegen-units = 1
lto = true

[lib]
name = "cocoindex_engine"
crate-type = ["cdylib"]

[dependencies]
pyo3 = { version = "0.24.1", features = ["chrono"] }
pythonize = "0.24.0"
pyo3-async-runtimes = { version = "0.24.0", features = ["tokio-runtime"] }

anyhow = { version = "1.0.97", features = ["std"] }
async-trait = "0.1.88"
axum = "0.7.9"
axum-extra = { version = "0.9.6", features = ["query"] }
base64 = "0.22.1"
chrono = "0.4.40"
config = "0.14.1"
const_format = "0.2.34"
futures = "0.3.31"
log = "0.4.27"
regex = "1.11.1"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
sqlx = { version = "0.8.3", features = [
    "chrono",
    "postgres",
    "runtime-tokio",
    "uuid",
] }
tokio = { version = "1.44.1", features = [
    "macros",
    "rt-multi-thread",
    "full",
    "tracing",
    "fs",
    "sync",
] }
tower = "0.5.2"
tower-http = { version = "0.6.2", features = ["cors", "trace"] }
indexmap = { version = "2.8.0", features = ["serde"] }
blake2 = "0.10.6"
pgvector = { version = "0.4.0", features = ["sqlx"] }
indenter = "0.3.3"
itertools = "0.14.0"
derivative = "2.2.0"
hex = "0.4.3"
schemars = "0.8.22"
env_logger = "0.11.7"
reqwest = { version = "0.12.15", default-features = false, features = [
    "json",
    "rustls-tls",
] }
async-openai = "0.28.0"
tree-sitter = "0.25.3"
tree-sitter-language = "0.1.5"
# Per language tree-sitter parsers
tree-sitter-c = "0.23.4"
tree-sitter-cpp = "0.23.4"
tree-sitter-c-sharp = "0.23.1"
tree-sitter-css = "0.23.2"
tree-sitter-fortran = "0.5.1"
tree-sitter-go = "0.23.4"
tree-sitter-html = "0.23.2"
tree-sitter-java = "0.23.5"
tree-sitter-javascript = "0.23.1"
tree-sitter-json = "0.24.8"
tree-sitter-md = "0.3.2"
tree-sitter-pascal = "0.10.0"
tree-sitter-php = "0.23.11"
tree-sitter-python = "0.23.6"
tree-sitter-r = "1.1.0"
tree-sitter-ruby = "0.23.1"
tree-sitter-rust = "0.23.2"
tree-sitter-scala = "0.23.4"
tree-sitter-sequel = "0.3.8"
tree-sitter-swift = "0.7.0"
tree-sitter-toml-ng = "0.7.0"
tree-sitter-typescript = "0.23.2"
tree-sitter-xml = "0.7.0"
tree-sitter-yaml = "0.7.0"

globset = "0.4.16"
unicase = "2.8.1"
google-drive3 = "6.0.0"
hyper-util = "0.1.11"
hyper-rustls = { version = "0.27.5" }
yup-oauth2 = "12.1.0"
rustls = { version = "0.23.25" }
http-body-util = "0.1.3"
yaml-rust2 = "0.10.1"
urlencoding = "2.1.3"
qdrant-client = "1.13.0"
uuid = { version = "1.16.0", features = ["serde", "v4", "v8"] }
tokio-stream = "0.1.17"
async-stream = "0.3.6"
neo4rs = "0.8.0"
bytes = "1.10.1"
rand = "0.9.0"
indoc = "2.0.6"
owo-colors = "4.2.0"
json5 = "0.4.1"
aws-config = "1.6.2"
aws-sdk-s3 = "1.85.0"
aws-sdk-sqs = "1.67.0"
