{"name": "cocoindex-docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "3.7.0", "@docusaurus/plugin-client-redirects": "^3.7.0", "@docusaurus/preset-classic": "3.7.0", "@docusaurus/theme-mermaid": "^3.7.0", "@mdx-js/react": "^3.0.0", "clsx": "^2.0.0", "mixpanel-browser": "^2.59.0", "posthog-docusaurus": "^2.0.2", "prism-react-renderer": "^2.4.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-player": "^2.16.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.7.0", "@docusaurus/tsconfig": "3.7.0", "@docusaurus/types": "3.7.0", "typescript": "~5.8.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}