{"name": "tree-sitter", "version": "0.21.1", "description": "Incremental parsers for node", "author": "<PERSON>", "contributors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "license": "MIT", "repository": {"type": "git", "url": "http://github.com/tree-sitter/node-tree-sitter.git"}, "keywords": ["incremental", "parsing", "tree-sitter"], "main": "index.js", "types": "tree-sitter.d.ts", "files": ["binding.gyp", "tree-sitter.d.ts", "prebuilds/*", "src/*", "vendor/tree-sitter/lib/include/*", "vendor/tree-sitter/lib/src/*"], "dependencies": {"node-addon-api": "^8.0.0", "node-gyp-build": "^4.8.0"}, "devDependencies": {"@types/node": "^20.11.30", "chai": "^4.3.10", "jest": "^29.7.0", "mocha": "^8.4.0", "node-gyp": "^10.0.1", "prebuildify": "^6.0.0", "tmp": "^0.2.3", "tree-sitter-c": "github:amaanq/tree-sitter-c#napi", "tree-sitter-embedded-template": "github:tree-sitter/tree-sitter-embedded-template#napi", "tree-sitter-html": "github:amaanq/tree-sitter-html#napi", "tree-sitter-java": "github:amaanq/tree-sitter-java#napi", "tree-sitter-javascript": "github:amaanq/tree-sitter-javascript#napi", "tree-sitter-json": "github:tree-sitter/tree-sitter-json#napi", "tree-sitter-python": "github:amaanq/tree-sitter-python#napi", "tree-sitter-ruby": "github:tree-sitter/tree-sitter-ruby#napi", "tree-sitter-rust": "github:amaanq/tree-sitter-rust#napi"}, "scripts": {"install": "node-gyp-build", "build": "prebuildify --napi --strip", "rebuild": "node-gyp rebuild", "test": "mocha && jest"}, "publishConfig": {"access": "public"}}