# ICU Parts

This directory contains a small subset of files from the Unicode organization's [ICU repository](https://github.com/unicode-org/icu).

### License

The license for these files is contained in the `LICENSE` file within this directory.

### Contents

* Source files taken from the [`icu4c/source/common/unicode`](https://github.com/unicode-org/icu/tree/552b01f61127d30d6589aa4bf99468224979b661/icu4c/source/common/unicode) directory:
  * `utf8.h`
  * `utf16.h`
  * `umachine.h`
* Empty source files that are referenced by the above source files, but whose original contents in `libicu` are not needed:
  * `ptypes.h`
  * `urename.h`
  * `utf.h`
* `ICU_SHA` - File containing the Git SHA of the commit in the `icu` repository from which the files were obtained.
* `LICENSE` - The license file from the [`icu4c`](https://github.com/unicode-org/icu/tree/552b01f61127d30d6589aa4bf99468224979b661/icu4c) directory of the `icu` repository.
* `README.md` - This text file.

### Updating ICU

To incorporate changes from the upstream `icu` repository:

* Update `ICU_SHA` with the new Git SHA.
* Update `LICENSE` with the license text from the directory mentioned above.
* Update `utf8.h`, `utf16.h`, and `umachine.h` with their new contents in the `icu` repository.
