{"$schema": "https://tree-sitter.github.io/tree-sitter/assets/schemas/grammar.schema.json", "name": "javascript", "word": "identifier", "rules": {"program": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "hash_bang_line"}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "statement"}}]}, "hash_bang_line": {"type": "PATTERN", "value": "#!.*"}, "export_statement": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "export"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "*"}, {"type": "SYMBOL", "name": "_from_clause"}]}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "namespace_export"}, {"type": "SYMBOL", "name": "_from_clause"}]}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "export_clause"}, {"type": "SYMBOL", "name": "_from_clause"}]}, {"type": "SYMBOL", "name": "export_clause"}]}, {"type": "SYMBOL", "name": "_semicolon"}]}, {"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "FIELD", "name": "decorator", "content": {"type": "SYMBOL", "name": "decorator"}}}, {"type": "STRING", "value": "export"}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "declaration", "content": {"type": "SYMBOL", "name": "declaration"}}, {"type": "SEQ", "members": [{"type": "STRING", "value": "default"}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "declaration", "content": {"type": "SYMBOL", "name": "declaration"}}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "SYMBOL", "name": "_semicolon"}]}]}]}]}]}]}, "namespace_export": {"type": "SEQ", "members": [{"type": "STRING", "value": "*"}, {"type": "STRING", "value": "as"}, {"type": "SYMBOL", "name": "_module_export_name"}]}, "export_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "export_specifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "export_specifier"}]}}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}, {"type": "STRING", "value": "}"}]}, "export_specifier": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "_module_export_name"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "as"}, {"type": "FIELD", "name": "alias", "content": {"type": "SYMBOL", "name": "_module_export_name"}}]}, {"type": "BLANK"}]}]}, "_module_export_name": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "string"}]}, "declaration": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "function_declaration"}, {"type": "SYMBOL", "name": "generator_function_declaration"}, {"type": "SYMBOL", "name": "class_declaration"}, {"type": "SYMBOL", "name": "lexical_declaration"}, {"type": "SYMBOL", "name": "variable_declaration"}]}, "import": {"type": "TOKEN", "content": {"type": "STRING", "value": "import"}}, "import_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "import"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "import_clause"}, {"type": "SYMBOL", "name": "_from_clause"}]}, {"type": "FIELD", "name": "source", "content": {"type": "SYMBOL", "name": "string"}}]}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "import_attribute"}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "_semicolon"}]}, "import_clause": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "namespace_import"}, {"type": "SYMBOL", "name": "named_imports"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "namespace_import"}, {"type": "SYMBOL", "name": "named_imports"}]}]}, {"type": "BLANK"}]}]}]}, "_from_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "from"}, {"type": "FIELD", "name": "source", "content": {"type": "SYMBOL", "name": "string"}}]}, "namespace_import": {"type": "SEQ", "members": [{"type": "STRING", "value": "*"}, {"type": "STRING", "value": "as"}, {"type": "SYMBOL", "name": "identifier"}]}, "named_imports": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "import_specifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "import_specifier"}]}}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}, {"type": "STRING", "value": "}"}]}, "import_specifier": {"type": "CHOICE", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "_module_export_name"}}, {"type": "STRING", "value": "as"}, {"type": "FIELD", "name": "alias", "content": {"type": "SYMBOL", "name": "identifier"}}]}]}, "import_attribute": {"type": "SEQ", "members": [{"type": "STRING", "value": "with"}, {"type": "SYMBOL", "name": "object"}]}, "statement": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "export_statement"}, {"type": "SYMBOL", "name": "import_statement"}, {"type": "SYMBOL", "name": "debugger_statement"}, {"type": "SYMBOL", "name": "expression_statement"}, {"type": "SYMBOL", "name": "declaration"}, {"type": "SYMBOL", "name": "statement_block"}, {"type": "SYMBOL", "name": "if_statement"}, {"type": "SYMBOL", "name": "switch_statement"}, {"type": "SYMBOL", "name": "for_statement"}, {"type": "SYMBOL", "name": "for_in_statement"}, {"type": "SYMBOL", "name": "while_statement"}, {"type": "SYMBOL", "name": "do_statement"}, {"type": "SYMBOL", "name": "try_statement"}, {"type": "SYMBOL", "name": "with_statement"}, {"type": "SYMBOL", "name": "break_statement"}, {"type": "SYMBOL", "name": "continue_statement"}, {"type": "SYMBOL", "name": "return_statement"}, {"type": "SYMBOL", "name": "throw_statement"}, {"type": "SYMBOL", "name": "empty_statement"}, {"type": "SYMBOL", "name": "labeled_statement"}]}, "expression_statement": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_expressions"}, {"type": "SYMBOL", "name": "_semicolon"}]}, "variable_declaration": {"type": "SEQ", "members": [{"type": "STRING", "value": "var"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "variable_declarator"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "variable_declarator"}]}}]}, {"type": "SYMBOL", "name": "_semicolon"}]}, "lexical_declaration": {"type": "SEQ", "members": [{"type": "FIELD", "name": "kind", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "let"}, {"type": "STRING", "value": "const"}]}}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "variable_declarator"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "variable_declarator"}]}}]}, {"type": "SYMBOL", "name": "_semicolon"}]}, "variable_declarator": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_destructuring_pattern"}]}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_initializer"}, {"type": "BLANK"}]}]}, "statement_block": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "statement"}}, {"type": "STRING", "value": "}"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_automatic_semicolon"}, {"type": "BLANK"}]}]}}, "else_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "else"}, {"type": "SYMBOL", "name": "statement"}]}, "if_statement": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "if"}, {"type": "FIELD", "name": "condition", "content": {"type": "SYMBOL", "name": "parenthesized_expression"}}, {"type": "FIELD", "name": "consequence", "content": {"type": "SYMBOL", "name": "statement"}}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "alternative", "content": {"type": "SYMBOL", "name": "else_clause"}}, {"type": "BLANK"}]}]}}, "switch_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "switch"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "parenthesized_expression"}}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "switch_body"}}]}, "for_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "for"}, {"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "initializer", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "lexical_declaration"}, {"type": "SYMBOL", "name": "variable_declaration"}]}}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "initializer", "content": {"type": "SYMBOL", "name": "_expressions"}}, {"type": "STRING", "value": ";"}]}, {"type": "FIELD", "name": "initializer", "content": {"type": "SYMBOL", "name": "empty_statement"}}]}, {"type": "FIELD", "name": "condition", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_expressions"}, {"type": "STRING", "value": ";"}]}, {"type": "SYMBOL", "name": "empty_statement"}]}}, {"type": "FIELD", "name": "increment", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_expressions"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": ")"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement"}}]}, "for_in_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "for"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "await"}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "_for_header"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement"}}]}, "_for_header": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "left", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_lhs_expression"}, {"type": "SYMBOL", "name": "parenthesized_expression"}]}}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "kind", "content": {"type": "STRING", "value": "var"}}, {"type": "FIELD", "name": "left", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_destructuring_pattern"}]}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_initializer"}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "kind", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "let"}, {"type": "STRING", "value": "const"}]}}, {"type": "FIELD", "name": "left", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_destructuring_pattern"}]}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_automatic_semicolon"}, {"type": "BLANK"}]}]}]}, {"type": "FIELD", "name": "operator", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "in"}, {"type": "STRING", "value": "of"}]}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expressions"}}, {"type": "STRING", "value": ")"}]}, "while_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "while"}, {"type": "FIELD", "name": "condition", "content": {"type": "SYMBOL", "name": "parenthesized_expression"}}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement"}}]}, "do_statement": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "do"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement"}}, {"type": "STRING", "value": "while"}, {"type": "FIELD", "name": "condition", "content": {"type": "SYMBOL", "name": "parenthesized_expression"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_semicolon"}, {"type": "BLANK"}]}]}}, "try_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "try"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "handler", "content": {"type": "SYMBOL", "name": "catch_clause"}}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "finalizer", "content": {"type": "SYMBOL", "name": "finally_clause"}}, {"type": "BLANK"}]}]}, "with_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "with"}, {"type": "FIELD", "name": "object", "content": {"type": "SYMBOL", "name": "parenthesized_expression"}}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement"}}]}, "break_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "break"}, {"type": "FIELD", "name": "label", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "identifier"}, "named": true, "value": "statement_identifier"}, {"type": "BLANK"}]}}, {"type": "SYMBOL", "name": "_semicolon"}]}, "continue_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "continue"}, {"type": "FIELD", "name": "label", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "identifier"}, "named": true, "value": "statement_identifier"}, {"type": "BLANK"}]}}, {"type": "SYMBOL", "name": "_semicolon"}]}, "debugger_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "debugger"}, {"type": "SYMBOL", "name": "_semicolon"}]}, "return_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "return"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_expressions"}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "_semicolon"}]}, "throw_statement": {"type": "SEQ", "members": [{"type": "STRING", "value": "throw"}, {"type": "SYMBOL", "name": "_expressions"}, {"type": "SYMBOL", "name": "_semicolon"}]}, "empty_statement": {"type": "STRING", "value": ";"}, "labeled_statement": {"type": "PREC_DYNAMIC", "value": -1, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "label", "content": {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_reserved_identifier"}]}, "named": true, "value": "statement_identifier"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement"}}]}}, "switch_body": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "switch_case"}, {"type": "SYMBOL", "name": "switch_default"}]}}, {"type": "STRING", "value": "}"}]}, "switch_case": {"type": "SEQ", "members": [{"type": "STRING", "value": "case"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "_expressions"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "statement"}}}]}, "switch_default": {"type": "SEQ", "members": [{"type": "STRING", "value": "default"}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "body", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "statement"}}}]}, "catch_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "catch"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "FIELD", "name": "parameter", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_destructuring_pattern"}]}}, {"type": "STRING", "value": ")"}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}]}, "finally_clause": {"type": "SEQ", "members": [{"type": "STRING", "value": "finally"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}]}, "parenthesized_expression": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "SYMBOL", "name": "_expressions"}, {"type": "STRING", "value": ")"}]}, "_expressions": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "sequence_expression"}]}, "expression": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "primary_expression"}, {"type": "SYMBOL", "name": "_jsx_element"}, {"type": "SYMBOL", "name": "assignment_expression"}, {"type": "SYMBOL", "name": "augmented_assignment_expression"}, {"type": "SYMBOL", "name": "await_expression"}, {"type": "SYMBOL", "name": "unary_expression"}, {"type": "SYMBOL", "name": "binary_expression"}, {"type": "SYMBOL", "name": "ternary_expression"}, {"type": "SYMBOL", "name": "update_expression"}, {"type": "SYMBOL", "name": "new_expression"}, {"type": "SYMBOL", "name": "yield_expression"}]}, "primary_expression": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "subscript_expression"}, {"type": "SYMBOL", "name": "member_expression"}, {"type": "SYMBOL", "name": "parenthesized_expression"}, {"type": "SYMBOL", "name": "_identifier"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_reserved_identifier"}, "named": true, "value": "identifier"}, {"type": "SYMBOL", "name": "this"}, {"type": "SYMBOL", "name": "super"}, {"type": "SYMBOL", "name": "number"}, {"type": "SYMBOL", "name": "string"}, {"type": "SYMBOL", "name": "template_string"}, {"type": "SYMBOL", "name": "regex"}, {"type": "SYMBOL", "name": "true"}, {"type": "SYMBOL", "name": "false"}, {"type": "SYMBOL", "name": "null"}, {"type": "SYMBOL", "name": "object"}, {"type": "SYMBOL", "name": "array"}, {"type": "SYMBOL", "name": "function_expression"}, {"type": "SYMBOL", "name": "arrow_function"}, {"type": "SYMBOL", "name": "generator_function"}, {"type": "SYMBOL", "name": "class"}, {"type": "SYMBOL", "name": "meta_property"}, {"type": "SYMBOL", "name": "call_expression"}]}, "yield_expression": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "yield"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "*"}, {"type": "SYMBOL", "name": "expression"}]}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "BLANK"}]}]}]}}, "object": {"type": "PREC", "value": "object", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pair"}, {"type": "SYMBOL", "name": "spread_element"}, {"type": "SYMBOL", "name": "method_definition"}, {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_reserved_identifier"}]}, "named": true, "value": "shorthand_property_identifier"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pair"}, {"type": "SYMBOL", "name": "spread_element"}, {"type": "SYMBOL", "name": "method_definition"}, {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_reserved_identifier"}]}, "named": true, "value": "shorthand_property_identifier"}]}, {"type": "BLANK"}]}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "}"}]}}, "object_pattern": {"type": "PREC", "value": "object", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pair_pattern"}, {"type": "SYMBOL", "name": "rest_pattern"}, {"type": "SYMBOL", "name": "object_assignment_pattern"}, {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_reserved_identifier"}]}, "named": true, "value": "shorthand_property_identifier_pattern"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pair_pattern"}, {"type": "SYMBOL", "name": "rest_pattern"}, {"type": "SYMBOL", "name": "object_assignment_pattern"}, {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_reserved_identifier"}]}, "named": true, "value": "shorthand_property_identifier_pattern"}]}, {"type": "BLANK"}]}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "}"}]}}, "assignment_pattern": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "pattern"}}, {"type": "STRING", "value": "="}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}, "object_assignment_pattern": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_reserved_identifier"}, {"type": "SYMBOL", "name": "identifier"}]}, "named": true, "value": "shorthand_property_identifier_pattern"}, {"type": "SYMBOL", "name": "_destructuring_pattern"}]}}, {"type": "STRING", "value": "="}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}, "array": {"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "spread_element"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "spread_element"}]}, {"type": "BLANK"}]}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "]"}]}, "array_pattern": {"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pattern"}, {"type": "SYMBOL", "name": "assignment_pattern"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pattern"}, {"type": "SYMBOL", "name": "assignment_pattern"}]}, {"type": "BLANK"}]}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "]"}]}, "_jsx_element": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "jsx_element"}, {"type": "SYMBOL", "name": "jsx_self_closing_element"}]}, "jsx_element": {"type": "SEQ", "members": [{"type": "FIELD", "name": "open_tag", "content": {"type": "SYMBOL", "name": "jsx_opening_element"}}, {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "_jsx_child"}}, {"type": "FIELD", "name": "close_tag", "content": {"type": "SYMBOL", "name": "jsx_closing_element"}}]}, "html_character_reference": {"type": "PATTERN", "value": "&(#([xX][0-9a-fA-F]{1,6}|[0-9]{1,5})|[A-Za-z]{1,30});"}, "jsx_expression": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "sequence_expression"}, {"type": "SYMBOL", "name": "spread_element"}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "}"}]}, "_jsx_child": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "jsx_text"}, {"type": "SYMBOL", "name": "html_character_reference"}, {"type": "SYMBOL", "name": "_jsx_element"}, {"type": "SYMBOL", "name": "jsx_expression"}]}, "jsx_opening_element": {"type": "PREC_DYNAMIC", "value": -1, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "<"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "_jsx_element_name"}}, {"type": "REPEAT", "content": {"type": "FIELD", "name": "attribute", "content": {"type": "SYMBOL", "name": "_jsx_attribute"}}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ">"}]}}, "jsx_identifier": {"type": "PATTERN", "value": "[a-zA-Z_$][a-zA-Z\\d_$]*-[a-zA-Z\\d_$\\-]*"}, "_jsx_identifier": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "jsx_identifier"}, "named": true, "value": "identifier"}, {"type": "SYMBOL", "name": "identifier"}]}, "nested_identifier": {"type": "PREC", "value": "member", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "object", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "nested_identifier"}, "named": true, "value": "member_expression"}]}}, {"type": "STRING", "value": "."}, {"type": "FIELD", "name": "property", "content": {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "identifier"}, "named": true, "value": "property_identifier"}}]}}, "jsx_namespace_name": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_jsx_identifier"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "_jsx_identifier"}]}, "_jsx_element_name": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_jsx_identifier"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "nested_identifier"}, "named": true, "value": "member_expression"}, {"type": "SYMBOL", "name": "jsx_namespace_name"}]}, "jsx_closing_element": {"type": "SEQ", "members": [{"type": "STRING", "value": "</"}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "_jsx_element_name"}}, {"type": "BLANK"}]}, {"type": "STRING", "value": ">"}]}, "jsx_self_closing_element": {"type": "SEQ", "members": [{"type": "STRING", "value": "<"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "_jsx_element_name"}}, {"type": "REPEAT", "content": {"type": "FIELD", "name": "attribute", "content": {"type": "SYMBOL", "name": "_jsx_attribute"}}}, {"type": "STRING", "value": "/>"}]}, "_jsx_attribute": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "jsx_attribute"}, {"type": "SYMBOL", "name": "jsx_expression"}]}, "_jsx_attribute_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_jsx_identifier"}, "named": true, "value": "property_identifier"}, {"type": "SYMBOL", "name": "jsx_namespace_name"}]}, "jsx_attribute": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_jsx_attribute_name"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "="}, {"type": "SYMBOL", "name": "_jsx_attribute_value"}]}, {"type": "BLANK"}]}]}, "_jsx_string": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "\""}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "unescaped_double_jsx_string_fragment"}, "named": true, "value": "string_fragment"}, {"type": "SYMBOL", "name": "html_character_reference"}]}}, {"type": "STRING", "value": "\""}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "'"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "unescaped_single_jsx_string_fragment"}, "named": true, "value": "string_fragment"}, {"type": "SYMBOL", "name": "html_character_reference"}]}}, {"type": "STRING", "value": "'"}]}]}, "unescaped_double_jsx_string_fragment": {"type": "IMMEDIATE_TOKEN", "content": {"type": "PREC", "value": 1, "content": {"type": "PATTERN", "value": "([^\"&]|&[^#A-Za-z])+"}}}, "unescaped_single_jsx_string_fragment": {"type": "IMMEDIATE_TOKEN", "content": {"type": "PREC", "value": 1, "content": {"type": "PATTERN", "value": "([^'&]|&[^#A-Za-z])+"}}}, "_jsx_attribute_value": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_jsx_string"}, "named": true, "value": "string"}, {"type": "SYMBOL", "name": "jsx_expression"}, {"type": "SYMBOL", "name": "_jsx_element"}]}, "class": {"type": "PREC", "value": "literal", "content": {"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "FIELD", "name": "decorator", "content": {"type": "SYMBOL", "name": "decorator"}}}, {"type": "STRING", "value": "class"}, {"type": "FIELD", "name": "name", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "BLANK"}]}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "class_heritage"}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "class_body"}}]}}, "class_declaration": {"type": "PREC", "value": "declaration", "content": {"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "FIELD", "name": "decorator", "content": {"type": "SYMBOL", "name": "decorator"}}}, {"type": "STRING", "value": "class"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "class_heritage"}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "class_body"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_automatic_semicolon"}, {"type": "BLANK"}]}]}}, "class_heritage": {"type": "SEQ", "members": [{"type": "STRING", "value": "extends"}, {"type": "SYMBOL", "name": "expression"}]}, "function_expression": {"type": "PREC", "value": "literal", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "function"}, {"type": "FIELD", "name": "name", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "BLANK"}]}}, {"type": "SYMBOL", "name": "_call_signature"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}]}}, "function_declaration": {"type": "PREC_RIGHT", "value": "declaration", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "function"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "SYMBOL", "name": "_call_signature"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_automatic_semicolon"}, {"type": "BLANK"}]}]}}, "generator_function": {"type": "PREC", "value": "literal", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "function"}, {"type": "STRING", "value": "*"}, {"type": "FIELD", "name": "name", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "BLANK"}]}}, {"type": "SYMBOL", "name": "_call_signature"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}]}}, "generator_function_declaration": {"type": "PREC_RIGHT", "value": "declaration", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "STRING", "value": "function"}, {"type": "STRING", "value": "*"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "SYMBOL", "name": "_call_signature"}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_automatic_semicolon"}, {"type": "BLANK"}]}]}}, "arrow_function": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "parameter", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_reserved_identifier"}, "named": true, "value": "identifier"}, {"type": "SYMBOL", "name": "identifier"}]}}, {"type": "SYMBOL", "name": "_call_signature"}]}, {"type": "STRING", "value": "=>"}, {"type": "FIELD", "name": "body", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "statement_block"}]}}]}, "_call_signature": {"type": "FIELD", "name": "parameters", "content": {"type": "SYMBOL", "name": "formal_parameters"}}, "_formal_parameter": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pattern"}, {"type": "SYMBOL", "name": "assignment_pattern"}]}, "optional_chain": {"type": "STRING", "value": "?."}, "call_expression": {"type": "CHOICE", "members": [{"type": "PREC", "value": "call", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "function", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "import"}]}}, {"type": "FIELD", "name": "arguments", "content": {"type": "SYMBOL", "name": "arguments"}}]}}, {"type": "PREC", "value": "template_call", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "function", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "primary_expression"}, {"type": "SYMBOL", "name": "new_expression"}]}}, {"type": "FIELD", "name": "arguments", "content": {"type": "SYMBOL", "name": "template_string"}}]}}, {"type": "PREC", "value": "member", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "function", "content": {"type": "SYMBOL", "name": "primary_expression"}}, {"type": "FIELD", "name": "optional_chain", "content": {"type": "SYMBOL", "name": "optional_chain"}}, {"type": "FIELD", "name": "arguments", "content": {"type": "SYMBOL", "name": "arguments"}}]}}]}, "new_expression": {"type": "PREC_RIGHT", "value": "new", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "new"}, {"type": "FIELD", "name": "constructor", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "primary_expression"}, {"type": "SYMBOL", "name": "new_expression"}]}}, {"type": "FIELD", "name": "arguments", "content": {"type": "CHOICE", "members": [{"type": "PREC_DYNAMIC", "value": 1, "content": {"type": "SYMBOL", "name": "arguments"}}, {"type": "BLANK"}]}}]}}, "await_expression": {"type": "PREC", "value": "unary_void", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "await"}, {"type": "SYMBOL", "name": "expression"}]}}, "member_expression": {"type": "PREC", "value": "member", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "object", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "primary_expression"}, {"type": "SYMBOL", "name": "import"}]}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "."}, {"type": "FIELD", "name": "optional_chain", "content": {"type": "SYMBOL", "name": "optional_chain"}}]}, {"type": "FIELD", "name": "property", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "private_property_identifier"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "identifier"}, "named": true, "value": "property_identifier"}]}}]}}, "subscript_expression": {"type": "PREC_RIGHT", "value": "member", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "object", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "primary_expression"}]}}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "optional_chain", "content": {"type": "SYMBOL", "name": "optional_chain"}}, {"type": "BLANK"}]}, {"type": "STRING", "value": "["}, {"type": "FIELD", "name": "index", "content": {"type": "SYMBOL", "name": "_expressions"}}, {"type": "STRING", "value": "]"}]}}, "_lhs_expression": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "member_expression"}, {"type": "SYMBOL", "name": "subscript_expression"}, {"type": "SYMBOL", "name": "_identifier"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_reserved_identifier"}, "named": true, "value": "identifier"}, {"type": "SYMBOL", "name": "_destructuring_pattern"}]}, "assignment_expression": {"type": "PREC_RIGHT", "value": "assign", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "parenthesized_expression"}, {"type": "SYMBOL", "name": "_lhs_expression"}]}}, {"type": "STRING", "value": "="}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, "_augmented_assignment_lhs": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "member_expression"}, {"type": "SYMBOL", "name": "subscript_expression"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_reserved_identifier"}, "named": true, "value": "identifier"}, {"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "parenthesized_expression"}]}, "augmented_assignment_expression": {"type": "PREC_RIGHT", "value": "assign", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_augmented_assignment_lhs"}}, {"type": "FIELD", "name": "operator", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "+="}, {"type": "STRING", "value": "-="}, {"type": "STRING", "value": "*="}, {"type": "STRING", "value": "/="}, {"type": "STRING", "value": "%="}, {"type": "STRING", "value": "^="}, {"type": "STRING", "value": "&="}, {"type": "STRING", "value": "|="}, {"type": "STRING", "value": ">>="}, {"type": "STRING", "value": ">>>="}, {"type": "STRING", "value": "<<="}, {"type": "STRING", "value": "**="}, {"type": "STRING", "value": "&&="}, {"type": "STRING", "value": "||="}, {"type": "STRING", "value": "??="}]}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, "_initializer": {"type": "SEQ", "members": [{"type": "STRING", "value": "="}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}]}, "_destructuring_pattern": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "object_pattern"}, {"type": "SYMBOL", "name": "array_pattern"}]}, "spread_element": {"type": "SEQ", "members": [{"type": "STRING", "value": "..."}, {"type": "SYMBOL", "name": "expression"}]}, "ternary_expression": {"type": "PREC_RIGHT", "value": "ternary", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "condition", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_ternary_qmark"}, "named": false, "value": "?"}, {"type": "FIELD", "name": "consequence", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "alternative", "content": {"type": "SYMBOL", "name": "expression"}}]}}, "binary_expression": {"type": "CHOICE", "members": [{"type": "PREC_LEFT", "value": "logical_and", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "&&"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "logical_or", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "||"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_shift", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": ">>"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_shift", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": ">>>"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_shift", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "<<"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "bitwise_and", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "&"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "bitwise_xor", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "^"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "bitwise_or", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "|"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_plus", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "+"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_plus", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "-"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_times", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "*"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_times", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "/"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_times", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "%"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_RIGHT", "value": "binary_exp", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "**"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_relation", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "<"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_relation", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "<="}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_equality", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "=="}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_equality", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "==="}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_equality", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "!="}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_equality", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "!=="}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_relation", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": ">="}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_relation", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": ">"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "ternary", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "??"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_relation", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "instanceof"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}, {"type": "PREC_LEFT", "value": "binary_relation", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "private_property_identifier"}]}}, {"type": "FIELD", "name": "operator", "content": {"type": "STRING", "value": "in"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "expression"}}]}}]}, "unary_expression": {"type": "PREC_LEFT", "value": "unary_void", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "operator", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "!"}, {"type": "STRING", "value": "~"}, {"type": "STRING", "value": "-"}, {"type": "STRING", "value": "+"}, {"type": "STRING", "value": "typeof"}, {"type": "STRING", "value": "void"}, {"type": "STRING", "value": "delete"}]}}, {"type": "FIELD", "name": "argument", "content": {"type": "SYMBOL", "name": "expression"}}]}}, "update_expression": {"type": "PREC_LEFT", "value": 0, "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "FIELD", "name": "argument", "content": {"type": "SYMBOL", "name": "expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "++"}, {"type": "STRING", "value": "--"}]}}]}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "operator", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "++"}, {"type": "STRING", "value": "--"}]}}, {"type": "FIELD", "name": "argument", "content": {"type": "SYMBOL", "name": "expression"}}]}]}}, "sequence_expression": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "expression"}]}}]}}, "string": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "\""}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "unescaped_double_string_fragment"}, "named": true, "value": "string_fragment"}, {"type": "SYMBOL", "name": "escape_sequence"}]}}, {"type": "STRING", "value": "\""}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "'"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "unescaped_single_string_fragment"}, "named": true, "value": "string_fragment"}, {"type": "SYMBOL", "name": "escape_sequence"}]}}, {"type": "STRING", "value": "'"}]}]}, "unescaped_double_string_fragment": {"type": "IMMEDIATE_TOKEN", "content": {"type": "PREC", "value": 1, "content": {"type": "PATTERN", "value": "[^\"\\\\\\r\\n]+"}}}, "unescaped_single_string_fragment": {"type": "IMMEDIATE_TOKEN", "content": {"type": "PREC", "value": 1, "content": {"type": "PATTERN", "value": "[^'\\\\\\r\\n]+"}}}, "escape_sequence": {"type": "IMMEDIATE_TOKEN", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "\\"}, {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[^xu0-7]"}, {"type": "PATTERN", "value": "[0-7]{1,3}"}, {"type": "PATTERN", "value": "x[0-9a-fA-F]{2}"}, {"type": "PATTERN", "value": "u[0-9a-fA-F]{4}"}, {"type": "PATTERN", "value": "u\\{[0-9a-fA-F]+\\}"}, {"type": "PATTERN", "value": "[\\r?][\\n\\u2028\\u2029]"}]}]}}, "comment": {"type": "TOKEN", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "//"}, {"type": "PATTERN", "value": "[^\\r\\n\\u2028\\u2029]*"}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "/*"}, {"type": "PATTERN", "value": "[^*]*\\*+([^/*][^*]*\\*+)*"}, {"type": "STRING", "value": "/"}]}]}}, "template_string": {"type": "SEQ", "members": [{"type": "STRING", "value": "`"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "SYMBOL", "name": "_template_chars"}, "named": true, "value": "string_fragment"}, {"type": "SYMBOL", "name": "escape_sequence"}, {"type": "SYMBOL", "name": "template_substitution"}]}}, {"type": "STRING", "value": "`"}]}, "template_substitution": {"type": "SEQ", "members": [{"type": "STRING", "value": "${"}, {"type": "SYMBOL", "name": "_expressions"}, {"type": "STRING", "value": "}"}]}, "regex": {"type": "SEQ", "members": [{"type": "STRING", "value": "/"}, {"type": "FIELD", "name": "pattern", "content": {"type": "SYMBOL", "name": "regex_pattern"}}, {"type": "IMMEDIATE_TOKEN", "content": {"type": "PREC", "value": 1, "content": {"type": "STRING", "value": "/"}}}, {"type": "CHOICE", "members": [{"type": "FIELD", "name": "flags", "content": {"type": "SYMBOL", "name": "regex_flags"}}, {"type": "BLANK"}]}]}, "regex_pattern": {"type": "IMMEDIATE_TOKEN", "content": {"type": "PREC", "value": -1, "content": {"type": "REPEAT1", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "\\"}, {"type": "PATTERN", "value": "."}]}, {"type": "PATTERN", "value": "[^\\]\\n\\\\]"}]}}, {"type": "STRING", "value": "]"}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "\\"}, {"type": "PATTERN", "value": "."}]}, {"type": "PATTERN", "value": "[^/\\\\\\[\\n]"}]}}}}, "regex_flags": {"type": "IMMEDIATE_TOKEN", "content": {"type": "PATTERN", "value": "[a-z]+"}}, "number": {"type": "TOKEN", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0x"}, {"type": "STRING", "value": "0X"}]}, {"type": "PATTERN", "value": "[\\da-fA-F](_?[\\da-fA-F])*"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0"}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0"}, {"type": "BLANK"}]}, {"type": "PATTERN", "value": "[1-9]"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "_"}, {"type": "BLANK"}]}, {"type": "PATTERN", "value": "\\d(_?\\d)*"}]}, {"type": "BLANK"}]}]}]}, {"type": "STRING", "value": "."}, {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "\\d(_?\\d)*"}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "e"}, {"type": "STRING", "value": "E"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "-"}, {"type": "STRING", "value": "+"}]}, {"type": "BLANK"}]}, {"type": "PATTERN", "value": "\\d(_?\\d)*"}]}]}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "."}, {"type": "PATTERN", "value": "\\d(_?\\d)*"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "e"}, {"type": "STRING", "value": "E"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "-"}, {"type": "STRING", "value": "+"}]}, {"type": "BLANK"}]}, {"type": "PATTERN", "value": "\\d(_?\\d)*"}]}]}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0"}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0"}, {"type": "BLANK"}]}, {"type": "PATTERN", "value": "[1-9]"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "_"}, {"type": "BLANK"}]}, {"type": "PATTERN", "value": "\\d(_?\\d)*"}]}, {"type": "BLANK"}]}]}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "e"}, {"type": "STRING", "value": "E"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "-"}, {"type": "STRING", "value": "+"}]}, {"type": "BLANK"}]}, {"type": "PATTERN", "value": "\\d(_?\\d)*"}]}]}]}, {"type": "PATTERN", "value": "\\d(_?\\d)*"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0b"}, {"type": "STRING", "value": "0B"}]}, {"type": "PATTERN", "value": "[0-1](_?[0-1])*"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0o"}, {"type": "STRING", "value": "0O"}]}, {"type": "PATTERN", "value": "[0-7](_?[0-7])*"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0x"}, {"type": "STRING", "value": "0X"}]}, {"type": "PATTERN", "value": "[\\da-fA-F](_?[\\da-fA-F])*"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0b"}, {"type": "STRING", "value": "0B"}]}, {"type": "PATTERN", "value": "[0-1](_?[0-1])*"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "0o"}, {"type": "STRING", "value": "0O"}]}, {"type": "PATTERN", "value": "[0-7](_?[0-7])*"}]}, {"type": "PATTERN", "value": "\\d(_?\\d)*"}]}, {"type": "STRING", "value": "n"}]}]}}, "_identifier": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "undefined"}, {"type": "SYMBOL", "name": "identifier"}]}, "identifier": {"type": "TOKEN", "content": {"type": "SEQ", "members": [{"type": "PATTERN", "value": "[^\\x00-\\x1F\\s\\p{Zs}0-9:;`\"'@#.,|^&<=>+\\-*/\\\\%?!~()\\[\\]{}\\uFEFF\\u2060\\u200B\\u2028\\u2029]|\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]+\\}"}, {"type": "REPEAT", "content": {"type": "PATTERN", "value": "[^\\x00-\\x1F\\s\\p{Zs}:;`\"'@#.,|^&<=>+\\-*/\\\\%?!~()\\[\\]{}\\uFEFF\\u2060\\u200B\\u2028\\u2029]|\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]+\\}"}}]}}, "private_property_identifier": {"type": "TOKEN", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "#"}, {"type": "PATTERN", "value": "[^\\x00-\\x1F\\s\\p{Zs}0-9:;`\"'@#.,|^&<=>+\\-*/\\\\%?!~()\\[\\]{}\\uFEFF\\u2060\\u200B\\u2028\\u2029]|\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]+\\}"}, {"type": "REPEAT", "content": {"type": "PATTERN", "value": "[^\\x00-\\x1F\\s\\p{Zs}:;`\"'@#.,|^&<=>+\\-*/\\\\%?!~()\\[\\]{}\\uFEFF\\u2060\\u200B\\u2028\\u2029]|\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]+\\}"}}]}}, "meta_property": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "new"}, {"type": "STRING", "value": "."}, {"type": "STRING", "value": "target"}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "import"}, {"type": "STRING", "value": "."}, {"type": "STRING", "value": "meta"}]}]}, "this": {"type": "STRING", "value": "this"}, "super": {"type": "STRING", "value": "super"}, "true": {"type": "STRING", "value": "true"}, "false": {"type": "STRING", "value": "false"}, "null": {"type": "STRING", "value": "null"}, "undefined": {"type": "STRING", "value": "undefined"}, "arguments": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "spread_element"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "expression"}, {"type": "SYMBOL", "name": "spread_element"}]}, {"type": "BLANK"}]}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "decorator": {"type": "SEQ", "members": [{"type": "STRING", "value": "@"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "decorator_member_expression"}, "named": true, "value": "member_expression"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "decorator_call_expression"}, "named": true, "value": "call_expression"}]}]}, "decorator_member_expression": {"type": "PREC", "value": "member", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "object", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "decorator_member_expression"}, "named": true, "value": "member_expression"}]}}, {"type": "STRING", "value": "."}, {"type": "FIELD", "name": "property", "content": {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "identifier"}, "named": true, "value": "property_identifier"}}]}}, "decorator_call_expression": {"type": "PREC", "value": "call", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "function", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "decorator_member_expression"}, "named": true, "value": "member_expression"}]}}, {"type": "FIELD", "name": "arguments", "content": {"type": "SYMBOL", "name": "arguments"}}]}}, "class_body": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "FIELD", "name": "member", "content": {"type": "SYMBOL", "name": "method_definition"}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "member", "content": {"type": "SYMBOL", "name": "field_definition"}}, {"type": "SYMBOL", "name": "_semicolon"}]}, {"type": "FIELD", "name": "member", "content": {"type": "SYMBOL", "name": "class_static_block"}}, {"type": "STRING", "value": ";"}]}}, {"type": "STRING", "value": "}"}]}, "field_definition": {"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "FIELD", "name": "decorator", "content": {"type": "SYMBOL", "name": "decorator"}}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "static"}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "property", "content": {"type": "SYMBOL", "name": "_property_name"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_initializer"}, {"type": "BLANK"}]}]}, "formal_parameters": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_formal_parameter"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "_formal_parameter"}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ","}, {"type": "BLANK"}]}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "class_static_block": {"type": "SEQ", "members": [{"type": "STRING", "value": "static"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_automatic_semicolon"}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}]}, "pattern": {"type": "PREC_DYNAMIC", "value": -1, "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_lhs_expression"}, {"type": "SYMBOL", "name": "rest_pattern"}]}}, "rest_pattern": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "..."}, {"type": "SYMBOL", "name": "_lhs_expression"}]}}, "method_definition": {"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "FIELD", "name": "decorator", "content": {"type": "SYMBOL", "name": "decorator"}}}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "static"}, {"type": "ALIAS", "content": {"type": "TOKEN", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "static"}, {"type": "PATTERN", "value": "\\s+"}, {"type": "STRING", "value": "get"}, {"type": "PATTERN", "value": "\\s*\\n"}]}}, "named": false, "value": "static get"}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "async"}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "get"}, {"type": "STRING", "value": "set"}, {"type": "STRING", "value": "*"}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "_property_name"}}, {"type": "FIELD", "name": "parameters", "content": {"type": "SYMBOL", "name": "formal_parameters"}}, {"type": "FIELD", "name": "body", "content": {"type": "SYMBOL", "name": "statement_block"}}]}, "pair": {"type": "SEQ", "members": [{"type": "FIELD", "name": "key", "content": {"type": "SYMBOL", "name": "_property_name"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "expression"}}]}, "pair_pattern": {"type": "SEQ", "members": [{"type": "FIELD", "name": "key", "content": {"type": "SYMBOL", "name": "_property_name"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "value", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "pattern"}, {"type": "SYMBOL", "name": "assignment_pattern"}]}}]}, "_property_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "_reserved_identifier"}]}, "named": true, "value": "property_identifier"}, {"type": "SYMBOL", "name": "private_property_identifier"}, {"type": "SYMBOL", "name": "string"}, {"type": "SYMBOL", "name": "number"}, {"type": "SYMBOL", "name": "computed_property_name"}]}, "computed_property_name": {"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "SYMBOL", "name": "expression"}, {"type": "STRING", "value": "]"}]}, "_reserved_identifier": {"type": "CHOICE", "members": [{"type": "STRING", "value": "get"}, {"type": "STRING", "value": "set"}, {"type": "STRING", "value": "async"}, {"type": "STRING", "value": "static"}, {"type": "STRING", "value": "export"}, {"type": "STRING", "value": "let"}]}, "_semicolon": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_automatic_semicolon"}, {"type": "STRING", "value": ";"}]}}, "extras": [{"type": "SYMBOL", "name": "comment"}, {"type": "SYMBOL", "name": "html_comment"}, {"type": "PATTERN", "value": "[\\s\\p{Zs}\\uFEFF\\u2028\\u2029\\u2060\\u200B]"}], "conflicts": [["primary_expression", "_property_name"], ["primary_expression", "_property_name", "arrow_function"], ["primary_expression", "arrow_function"], ["primary_expression", "method_definition"], ["primary_expression", "rest_pattern"], ["primary_expression", "pattern"], ["primary_expression", "_for_header"], ["variable_declarator", "_for_header"], ["array", "array_pattern"], ["object", "object_pattern"], ["assignment_expression", "pattern"], ["assignment_expression", "object_assignment_pattern"], ["labeled_statement", "_property_name"], ["computed_property_name", "array"], ["binary_expression", "_initializer"], ["class_static_block", "_property_name"]], "precedences": [[{"type": "STRING", "value": "member"}, {"type": "STRING", "value": "template_call"}, {"type": "STRING", "value": "call"}, {"type": "SYMBOL", "name": "update_expression"}, {"type": "STRING", "value": "unary_void"}, {"type": "STRING", "value": "binary_exp"}, {"type": "STRING", "value": "binary_times"}, {"type": "STRING", "value": "binary_plus"}, {"type": "STRING", "value": "binary_shift"}, {"type": "STRING", "value": "binary_compare"}, {"type": "STRING", "value": "binary_relation"}, {"type": "STRING", "value": "binary_equality"}, {"type": "STRING", "value": "bitwise_and"}, {"type": "STRING", "value": "bitwise_xor"}, {"type": "STRING", "value": "bitwise_or"}, {"type": "STRING", "value": "logical_and"}, {"type": "STRING", "value": "logical_or"}, {"type": "STRING", "value": "ternary"}, {"type": "SYMBOL", "name": "sequence_expression"}, {"type": "SYMBOL", "name": "arrow_function"}], [{"type": "STRING", "value": "assign"}, {"type": "SYMBOL", "name": "primary_expression"}], [{"type": "STRING", "value": "member"}, {"type": "STRING", "value": "template_call"}, {"type": "STRING", "value": "new"}, {"type": "STRING", "value": "call"}, {"type": "SYMBOL", "name": "expression"}], [{"type": "STRING", "value": "declaration"}, {"type": "STRING", "value": "literal"}], [{"type": "SYMBOL", "name": "primary_expression"}, {"type": "SYMBOL", "name": "statement_block"}, {"type": "STRING", "value": "object"}], [{"type": "SYMBOL", "name": "meta_property"}, {"type": "SYMBOL", "name": "import"}], [{"type": "SYMBOL", "name": "import_statement"}, {"type": "SYMBOL", "name": "import"}], [{"type": "SYMBOL", "name": "export_statement"}, {"type": "SYMBOL", "name": "primary_expression"}], [{"type": "SYMBOL", "name": "lexical_declaration"}, {"type": "SYMBOL", "name": "primary_expression"}]], "externals": [{"type": "SYMBOL", "name": "_automatic_semicolon"}, {"type": "SYMBOL", "name": "_template_chars"}, {"type": "SYMBOL", "name": "_ternary_qmark"}, {"type": "SYMBOL", "name": "html_comment"}, {"type": "STRING", "value": "||"}, {"type": "SYMBOL", "name": "escape_sequence"}, {"type": "SYMBOL", "name": "regex_pattern"}, {"type": "SYMBOL", "name": "jsx_text"}], "inline": ["_call_signature", "_formal_parameter", "_expressions", "_semicolon", "_identifier", "_reserved_identifier", "_jsx_attribute", "_jsx_element_name", "_jsx_child", "_jsx_element", "_jsx_attribute_name", "_jsx_attribute_value", "_jsx_identifier", "_lhs_expression"], "supertypes": ["statement", "declaration", "expression", "primary_expression", "pattern"]}