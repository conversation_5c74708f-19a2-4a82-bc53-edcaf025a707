{"cells": [{"cell_type": "markdown", "metadata": {"id": "Up70lME5E0Tc"}, "source": ["# ![icon.svg](https://cocoindex.io/icon.svg) Welcome to [Cocoindex](https://cocoindex.io/)\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "bJ3LGSyF9D1M"}, "source": ["\n", "#  ![icon.svg](https://cocoindex.io/icon.svg) This example will show you how you can get started with Cocoindex by building embedding for RAG"]}, {"cell_type": "markdown", "metadata": {"id": "ymNZ0fk09noG"}, "source": ["# Install Cocoindex and other required packages using pip"]}, {"cell_type": "markdown", "metadata": {"id": "s4MT3saT9COe"}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "rQcJanCi-W3I"}, "outputs": [], "source": ["%pip install cocoindex python-dotenv psycopg[binary,pool]"]}, {"cell_type": "markdown", "metadata": {"id": "Xh2sMemiA7_N"}, "source": ["# Grab some markdown files for demo"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "0Gi-MHrNA8sQ"}, "outputs": [], "source": ["!mkdir -p markdown_files && \\\n", "wget -P markdown_files https://raw.githubusercontent.com/cocoindex-io/cocoindex/refs/heads/main/examples/text_embedding/markdown_files/1706.03762v7.md && \\\n", "wget -P markdown_files https://raw.githubusercontent.com/cocoindex-io/cocoindex/refs/heads/main/examples/text_embedding/markdown_files/1810.04805v2.md && \\\n", "wget -P markdown_files https://raw.githubusercontent.com/cocoindex-io/cocoindex/refs/heads/main/examples/text_embedding/markdown_files/rfc8259.md"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hPctYqRAzgEq"}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"id": "ZEetEtmPAuZ-"}, "source": ["# Create a Postgres Server"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "lkATpKLUAuuY"}, "outputs": [], "source": ["# Update package lists\n", "!sudo apt-get update\n", "\n", "# Install PostgreSQL setup helper\n", "!sudo apt install -y postgresql-common\n", "\n", "# Automatically press Enter for the setup script\n", "!yes \"\" | sudo /usr/share/postgresql-common/pgdg/apt.postgresql.org.sh\n", "\n", "# Install PostgreSQL 17 and pgvector extension\n", "!sudo apt install -y postgresql-17 postgresql-17-pgvector\n", "\n", "# Start PostgreSQL service\n", "!sudo service postgresql start\n", "\n", "# Create user and database for cocoindex\n", "!sudo -u postgres psql -c \"CREATE USER cocoindex WITH PASSWORD 'cocoindex';\"\n", "!sudo -u postgres createdb cocoindex -O cocoindex\n", "\n", "# Enable the pgvector extension\n", "!sudo -u postgres psql -d cocoindex -c \"CREATE EXTENSION IF NOT EXISTS vector;\""]}, {"cell_type": "markdown", "metadata": {"id": "utZpExYkAzi6"}, "source": ["# Update .env with POSTGRES URL"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "X3P8pEUOA5D2"}, "outputs": [], "source": ["%%writefile .env\n", "COCOINDEX_DATABASE_URL=\"postgresql://cocoindex:cocoindex@localhost:5432/cocoindex\""]}, {"cell_type": "markdown", "metadata": {"id": "9zN612eW_1nX"}, "source": ["# Create a new file and import modules"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7HUYtsoN-10D"}, "outputs": [], "source": ["%%writefile main.py\n", "from dotenv import load_dotenv\n", "import os\n", "from psycopg_pool import ConnectionPool\n", "import cocoindex\n"]}, {"cell_type": "markdown", "metadata": {"id": "2DOY5Q27ADS2"}, "source": ["# Define your embedding function"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "L_puYY6FABbr"}, "outputs": [], "source": ["%%writefile -a main.py\n", "\n", "@cocoindex.transform_flow()\n", "def text_to_embedding(text: cocoindex.DataSlice[str]) -> cocoindex.DataSlice[list[float]]:\n", "    \"\"\"\n", "    Embed the text using a SentenceTransformer model.\n", "    This is shared logic between indexing and querying.\n", "    \"\"\"\n", "    return text.transform(\n", "        cocoindex.functions.SentenceTransformerEmbed(\n", "            model=\"sentence-transformers/all-MiniLM-L6-v2\"))\n"]}, {"cell_type": "markdown", "metadata": {"id": "H6j2aiRaAEKz"}, "source": ["# Define your flow"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oatJUXjAAEhE"}, "outputs": [], "source": ["%%writefile -a main.py\n", "\n", "@cocoindex.flow_def(name=\"TextEmbedding\")\n", "def text_embedding_flow(flow_builder: cocoindex.FlowBuilder, data_scope: cocoindex.DataScope):\n", "    \"\"\"\n", "    Define a flow that embeds text into a vector database.\n", "    \"\"\"\n", "    data_scope[\"documents\"] = flow_builder.add_source(\n", "        cocoindex.sources.LocalFile(path=\"markdown_files\"))\n", "\n", "    doc_embeddings = data_scope.add_collector()\n", "\n", "    with data_scope[\"documents\"].row() as doc:\n", "        doc[\"chunks\"] = doc[\"content\"].transform(\n", "            cocoindex.functions.SplitRecursively(),\n", "            language=\"markdown\", chunk_size=2000, chunk_overlap=500)\n", "\n", "        with doc[\"chunks\"].row() as chunk:\n", "            chunk[\"embedding\"] = text_to_embedding(chunk[\"text\"])\n", "            doc_embeddings.collect(filename=doc[\"filename\"], location=chunk[\"location\"],\n", "                                   text=chunk[\"text\"], embedding=chunk[\"embedding\"])\n", "\n", "    doc_embeddings.export(\n", "        \"doc_embeddings\",\n", "        cocoindex.storages.Postgres(),\n", "        primary_key_fields=[\"filename\", \"location\"],\n", "        vector_indexes=[\n", "            cocoindex.VectorIndexDef(\n", "                field_name=\"embedding\",\n", "                metric=cocoindex.VectorSimilarityMetric.COSINE_SIMILARITY)])\n"]}, {"cell_type": "markdown", "metadata": {"id": "KLb41N5UAFJx"}, "source": ["\n", "# Provide query logic\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tRdfIP6OAFe1"}, "outputs": [], "source": ["%%writefile -a main.py\n", "\n", "def search(pool: ConnectionPool, query: str, top_k: int = 5):\n", "    # Get the table name, for the export target in the text_embedding_flow above.\n", "    table_name = cocoindex.utils.get_target_storage_default_name(text_embedding_flow, \"doc_embeddings\")\n", "    # Evaluate the transform flow defined above with the input query, to get the embedding.\n", "    query_vector = text_to_embedding.eval(query)\n", "    # Run the query and get the results.\n", "    with pool.connection() as conn:\n", "        with conn.cursor() as cur:\n", "            cur.execute(f\"\"\"\n", "                SELECT filename, text, embedding <=> %s::vector AS distance\n", "                FROM {table_name} ORDER BY distance LIMIT %s\n", "            \"\"\", (query_vector, top_k))\n", "            return [\n", "                {\"filename\": row[0], \"text\": row[1], \"score\": 1.0 - row[2]}\n", "                for row in cur.fetchall()\n", "            ]\n"]}, {"cell_type": "markdown", "metadata": {"id": "IUBdoOmOAgwc"}, "source": ["# Define search function and main"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "W78hBbDiAhFh"}, "outputs": [], "source": ["%%writefile -a main.py\n", "\n", "def _main():\n", "    # Initialize the database connection pool.\n", "    pool = ConnectionPool(os.getenv(\"COCOINDEX_DATABASE_URL\"))\n", "    # Run queries in a loop to demonstrate the query capabilities.\n", "    while True:\n", "        try:\n", "            query = input(\"Enter search query (or Enter to quit): \")\n", "            if query == '':\n", "                break\n", "            # Run the query function with the database connection pool and the query.\n", "            results = search(pool, query)\n", "            print(\"\\nSearch results:\")\n", "            for result in results:\n", "                print(f\"[{result['score']:.3f}] {result['filename']}\")\n", "                print(f\"    {result['text']}\")\n", "                print(\"---\")\n", "            print()\n", "        except KeyboardInterrupt:\n", "            break\n", "\n", "if __name__ == \"__main__\":\n", "    load_dotenv(override=True)\n", "    cocoindex.init()\n", "    _main()\n"]}, {"cell_type": "markdown", "metadata": {"id": "I2oI_pjxCkRa"}, "source": ["# Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oBStjaI0Cli_"}, "outputs": [], "source": ["!yes yes | cocoindex setup main.py"]}, {"cell_type": "markdown", "metadata": {"id": "aPBDVrG_CmwH"}, "source": ["# Update"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "M9g6xIZHCn5T"}, "outputs": [], "source": ["!cocoindex update main.py"]}, {"cell_type": "markdown", "metadata": {"id": "nIM78MBRCppz"}, "source": ["# Run query"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6E-HR_KSCqzP"}, "outputs": [], "source": ["!python main.py"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}