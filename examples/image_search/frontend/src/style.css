body {
  font-family: system-ui, sans-serif;
  background: #f8fafc;
  margin: 0;
}

.container {
  max-width: 600px;
  margin: 40px auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px #0001;
  padding: 32px 24px 24px 24px;
}

h1 {
  text-align: center;
  margin-bottom: 24px;
}

.search-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
}
.search-bar input {
  flex: 1;
  padding: 8px 12px;
  font-size: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}
.search-bar button {
  padding: 8px 16px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.1s;
}
.search-bar button:disabled {
  background: #a5b4fc;
  cursor: not-allowed;
}

.results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.result-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: #f1f5f9;
  border-radius: 8px;
  padding: 12px 16px;
}
.result-img {
  width: 72px;
  height: 72px;
  object-fit: cover;
  border-radius: 8px;
  background: #e5e7eb;
}
.caption {
  flex: 1;
  font-size: 0.98rem;
  color: #222;
}
.score {
  font-size: 0.92rem;
  color: #555;
  min-width: 80px;
  text-align: right;
}
.error {
  color: #b91c1c;
  margin-bottom: 12px;
  text-align: center;
}
