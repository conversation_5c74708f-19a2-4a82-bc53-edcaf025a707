{"name": "cocoindex-split-recursively-ts", "version": "1.0.0", "description": "TypeScript port of the recursive text splitter from cocoindex", "main": "dist/split-recursively.js", "types": "dist/split-recursively.d.ts", "scripts": {"build": "tsc", "test": "npm run build && node dist/split-recursively.test.js", "dev": "ts-node src/split-recursively.test.ts", "example": "npm run build && node dist/example.js", "example:dev": "ts-node src/example.ts", "test:tree-sitter": "npm run build && node dist/tree-sitter-test.js", "test:tree-sitter:dev": "ts-node src/tree-sitter-test.ts", "test:static-mapping": "npm run build && node dist/static-mapping-test.js", "test:static-mapping:dev": "ts-node src/static-mapping-test.ts", "clean": "rm -rf dist", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["text-splitting", "chunking", "tree-sitter", "nlp", "text-processing"], "author": "Cocoindex Team", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"tree-sitter": "^0.21.0", "tree-sitter-javascript": "^0.23.1"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/cocoindex/cocoindex"}, "bugs": {"url": "https://github.com/cocoindex/cocoindex/issues"}, "homepage": "https://github.com/cocoindex/cocoindex#readme"}