[build-system]
requires = ["maturin>=1.7.8,<2.0"]
build-backend = "maturin"

[project]
name = "cocoindex"
dynamic = ["version"]
description = "With CocoIndex, users declare the transformation, CocoIndex creates & maintains an index, and keeps the derived index up to date based on source update, with minimal computation and changes."
authors = [{ name = "CocoIndex", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "sentence-transformers>=3.3.1",
    "click>=8.1.8",
    "rich>=14.0.0",
    "python-dotenv>=1.1.0",
]
license = "Apache-2.0"
urls = { Homepage = "https://cocoindex.io/" }

[project.scripts]
cocoindex = "cocoindex.cli:cli"

[tool.maturin]
bindings = "pyo3"
python-source = "python"
module-name = "cocoindex._engine"
features = ["pyo3/extension-module"]

[project.optional-dependencies]
test = ["pytest"]
dev = ["ruff"]

[tool.mypy]
python_version = "3.11"
# Disable for not as we have missing type annotations. See https://github.com/cocoindex-io/cocoindex/issues/539
#   strict = true
