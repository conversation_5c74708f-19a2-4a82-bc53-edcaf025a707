# This file is autogenerated by maturin v1.8.1
# To update, run
#
#    maturin generate-ci github
#
name: release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

permissions:
  contents: read

jobs:
  create-versioned-toml:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: ./.github/scripts/update_version.sh
      - uses: actions/upload-artifact@v4
        with:
          name: Cargo.toml
          path: Cargo.toml

  build:
    runs-on: ${{ matrix.platform.runner }}
    needs: [create-versioned-toml]
    strategy:
      matrix:
        platform:
          - { os: linux, runner: ubuntu-24.04, target: x86_64, container: "ghcr.io/rust-cross/manylinux_2_28-cross:x86_64" }
          - { os: linux, runner: ubuntu-24.04, target: aarch64, container: "ghcr.io/rust-cross/manylinux_2_28-cross:aarch64" }
          - { os: windows, runner: windows-latest, target: x64 }
          - { os: macos, runner: macos-13, target: x86_64 }
          - { os: macos, runner: macos-14, target: aarch64 }
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: Cargo.toml
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - name: Build wheels
        uses: PyO3/maturin-action@v1
        with:
          target: ${{ matrix.platform.target }}
          args: --release --out dist --find-interpreter
          sccache: 'true'
          manylinux: auto
          container: ${{ matrix.platform.container }}
      - name: Upload wheels
        uses: actions/upload-artifact@v4
        with:
          name: wheels-${{ matrix.platform.os }}-${{ matrix.platform.target }}
          path: dist

  sdist:
    runs-on: ubuntu-latest
    needs: [create-versioned-toml]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: Cargo.toml
      - name: Build sdist
        uses: PyO3/maturin-action@v1
        with:
          command: sdist
          args: --out dist
      - name: Upload sdist
        uses: actions/upload-artifact@v4
        with:
          name: wheels-sdist
          path: dist

  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [create-versioned-toml, build, sdist]
    permissions:
      # Use to sign the release artifacts
      id-token: write
      # Used to upload release artifacts
      contents: write
      # Used to generate artifact attestation
      attestations: write
    environment: release
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: Cargo.toml
      - uses: actions/download-artifact@v4
        with:
          pattern: wheels-*
      - name: Generate artifact attestation
        uses: actions/attest-build-provenance@v1
        with:
          subject-path: 'wheels-*/*'
      - name: Publish to PyPI
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        uses: PyO3/maturin-action@v1
        with:
          command: upload
          args: --non-interactive --skip-existing wheels-*/*
