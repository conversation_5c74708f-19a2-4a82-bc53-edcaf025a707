name: docs

on:
  pull_request:
    branches: [main]
    paths:
      - docs/**
      - ".github/workflows/docs.yml"
  push:
    branches: [main]
    paths:
      - docs/**
      - ".github/workflows/docs.yml"
  workflow_dispatch:

permissions:
  contents: write

jobs:
  test-deploy:
    if: github.event_name != 'push'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: docs
          path: src-staging
      - name: Move docs
        run: |
          shopt -s dotglob
          mv src-staging/docs/* .
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: yarn
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      - name: Test build website
        run: yarn build

  deploy-precheck:
    if: ${{ github.event_name != 'pull_request' }}
    runs-on: ubuntu-latest
    environment: docs-release
    outputs:
      gh-deploy-key: ${{ steps.gh-deploy-key.outputs.defined }}
    steps:
        - id: gh-deploy-key
          env:
              GH_PAGES_DEPLOY: ${{ secrets.GH_PAGES_DEPLOY }}
          if: "${{ env.GH_PAGES_DEPLOY != '' }}"
          run: echo "defined=true" >> $GITHUB_OUTPUT

  deploy:
    needs: [deploy-precheck]
    if: ${{ needs.deploy-precheck.outputs.gh-deploy-key == 'true' }}
    runs-on: ubuntu-latest
    environment: docs-release
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: docs
          path: src-staging
      - name: Move docs
        run: |
          shopt -s dotglob
          mv src-staging/docs/* .
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: yarn
      - uses: webfactory/ssh-agent@v0.5.0
        with:
          ssh-private-key: ${{ secrets.GH_PAGES_DEPLOY }}
      - name: Deploy to GitHub Pages
        env:
          USE_SSH: true
        run: |
          export COCOINDEX_DOCS_POSTHOG_API_KEY=${{ vars.COCOINDEX_DOCS_POSTHOG_API_KEY }}
          export COCOINDEX_DOCS_MIXPANEL_API_KEY=${{ vars.COCOINDEX_DOCS_MIXPANEL_API_KEY }}
          export COCOINDEX_DOCS_ALGOLIA_APP_ID=${{ vars.COCOINDEX_DOCS_ALGOLIA_APP_ID }}
          export COCOINDEX_DOCS_ALGOLIA_API_KEY=${{ vars.COCOINDEX_DOCS_ALGOLIA_API_KEY }}
          git config --global user.email "${{ vars.COCOINDEX_DOCS_DEPLOY_USER_EMAIL }}"
          git config --global user.name "${{ vars.COCOINDEX_DOCS_DEPLOY_USER_NAME }}"
          yarn install --frozen-lockfile
          yarn deploy
